#!/bin/bash

echo "🚀 Building Linux Baseline Checker v3.2.0 for Windows..."

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "❌ Error: Go not found. Please install Go first."
    exit 1
fi

echo "📦 Downloading dependencies..."
go mod tidy

if [ $? -ne 0 ]; then
    echo "❌ Failed to download dependencies!"
    exit 1
fi

echo "� Installing Fyne command line tool..."
go install fyne.io/tools/cmd/fyne@latest

echo "🔨 Packaging for Windows with Fyne..."
echo "⚠️  Note: Installing mingw-w64 for cross-compilation..."

# 尝试安装mingw-w64
if command -v apt-get &> /dev/null; then
    sudo apt-get update && sudo apt-get install -y gcc-mingw-w64-x86-64
elif command -v yum &> /dev/null; then
    sudo yum install -y mingw64-gcc
elif command -v brew &> /dev/null; then
    brew install mingw-w64
fi

# 使用Fyne打包命令（不使用图标）
CGO_ENABLED=1 GOOS=windows GOARCH=amd64 CC=x86_64-w64-mingw32-gcc fyne package -os windows -name "Linux基线检查工具"

# 检查生成的文件（fyne package可能生成不同的文件名）
if [ -f "Linux基线检查工具.exe" ]; then
    echo "✅ Windows build successful!"
    echo "📁 Executable: Linux基线检查工具.exe"
    echo "📊 File size: $(du -h 'Linux基线检查工具.exe' | cut -f1)"
elif [ -f "linux-baseline-checker.exe" ]; then
    echo "✅ Windows build successful!"
    echo "📁 Executable: linux-baseline-checker.exe"
    echo "📊 File size: $(du -h linux-baseline-checker.exe | cut -f1)"
    echo ""
    echo "🎯 Features included:"
    echo "   ✅ Modular architecture with clean separation"
    echo "   ✅ SSH connection management"
    echo "   ✅ Comprehensive baseline security checks (12+ check items)"
    echo "   ✅ Host and host group management"
    echo "   ✅ Batch scanning with progress tracking"
    echo "   ✅ Data persistence and export"
    echo "   ✅ Native Fyne GUI with professional look"
    echo ""
    echo "🚀 To run on Windows:"
    echo "   1. Copy the .exe file to Windows machine"
    echo "   2. Double-click to run"
    echo "   3. Data will be saved in the same directory as the executable"
    echo ""
    echo "📚 Project structure:"
    echo "   - core/: SSH, baseline checks, scanner engine"
    echo "   - data/: Models and storage management"
    echo "   - ui/: Fyne GUI components"
    echo "   - utils/: Helper functions"
    echo "   - config/: Configuration management"
else
    echo "❌ Windows build failed - executable not found!"
    exit 1
fi
