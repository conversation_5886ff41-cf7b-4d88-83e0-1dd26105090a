package config

import (
	"time"

	"linux-baseline-checker/data"
)

// Config 应用配置管理器
type Config struct {
	storage *data.Storage
	config  *data.AppConfig
}

// NewConfig 创建配置管理器
func NewConfig(storage *data.Storage) *Config {
	c := &Config{
		storage: storage,
	}

	// 加载配置
	c.loadConfig()

	return c
}

// loadConfig 加载配置
func (c *Config) loadConfig() {
	config, err := c.storage.LoadConfig()
	if err != nil {
		// 使用默认配置
		config = c.getDefaultConfig()
	}

	c.config = config
}

// getDefaultConfig 获取默认配置
func (c *Config) getDefaultConfig() *data.AppConfig {
	return &data.AppConfig{
		Version:        "3.2.0",
		DataDir:        c.storage.GetDataDir(),
		LogLevel:       "INFO",
		MaxConcurrency: 5,
		SSHTimeout:     30,
		ScanTimeout:    300,
		AutoSave:       true,
		BackupEnabled:  false,
		BackupInterval: 24,
		Theme:          "default",
		Language:       "zh-CN",
		WindowWidth:    900,
		WindowHeight:   600,
		WindowX:        -1, // -1表示居中
		WindowY:        -1, // -1表示居中
		CustomSettings: make(map[string]string),
		UpdatedAt:      time.Now(),
	}
}

// GetConfig 获取配置
func (c *Config) GetConfig() *data.AppConfig {
	return c.config
}

// SaveConfig 保存配置
func (c *Config) SaveConfig() error {
	return c.storage.SaveConfig(*c.config)
}

// 配置项访问器

// GetVersion 获取版本
func (c *Config) GetVersion() string {
	return c.config.Version
}

// GetDataDir 获取数据目录
func (c *Config) GetDataDir() string {
	return c.config.DataDir
}

// GetLogLevel 获取日志级别
func (c *Config) GetLogLevel() string {
	return c.config.LogLevel
}

// SetLogLevel 设置日志级别
func (c *Config) SetLogLevel(level string) {
	c.config.LogLevel = level
	c.config.UpdatedAt = time.Now()
}

// GetMaxConcurrency 获取最大并发数
func (c *Config) GetMaxConcurrency() int {
	return c.config.MaxConcurrency
}

// SetMaxConcurrency 设置最大并发数
func (c *Config) SetMaxConcurrency(concurrency int) {
	if concurrency < 1 {
		concurrency = 1
	} else if concurrency > 20 {
		concurrency = 20
	}

	c.config.MaxConcurrency = concurrency
	c.config.UpdatedAt = time.Now()
}

// GetSSHTimeout 获取SSH超时时间（秒）
func (c *Config) GetSSHTimeout() time.Duration {
	return time.Duration(c.config.SSHTimeout) * time.Second
}

// SetSSHTimeout 设置SSH超时时间（秒）
func (c *Config) SetSSHTimeout(seconds int) {
	if seconds < 5 {
		seconds = 5
	} else if seconds > 300 {
		seconds = 300
	}

	c.config.SSHTimeout = seconds
	c.config.UpdatedAt = time.Now()
}

// GetScanTimeout 获取扫描超时时间（秒）
func (c *Config) GetScanTimeout() time.Duration {
	return time.Duration(c.config.ScanTimeout) * time.Second
}

// SetScanTimeout 设置扫描超时时间（秒）
func (c *Config) SetScanTimeout(seconds int) {
	if seconds < 60 {
		seconds = 60
	} else if seconds > 3600 {
		seconds = 3600
	}

	c.config.ScanTimeout = seconds
	c.config.UpdatedAt = time.Now()
}

// GetAutoSave 获取自动保存设置
func (c *Config) GetAutoSave() bool {
	return c.config.AutoSave
}

// SetAutoSave 设置自动保存
func (c *Config) SetAutoSave(enabled bool) {
	c.config.AutoSave = enabled
	c.config.UpdatedAt = time.Now()
}

// GetBackupEnabled 获取备份启用状态
func (c *Config) GetBackupEnabled() bool {
	return c.config.BackupEnabled
}

// SetBackupEnabled 设置备份启用状态
func (c *Config) SetBackupEnabled(enabled bool) {
	c.config.BackupEnabled = enabled
	c.config.UpdatedAt = time.Now()
}

// GetBackupInterval 获取备份间隔（小时）
func (c *Config) GetBackupInterval() int {
	return c.config.BackupInterval
}

// SetBackupInterval 设置备份间隔（小时）
func (c *Config) SetBackupInterval(hours int) {
	if hours < 1 {
		hours = 1
	} else if hours > 168 { // 最多一周
		hours = 168
	}

	c.config.BackupInterval = hours
	c.config.UpdatedAt = time.Now()
}

// GetTheme 获取主题
func (c *Config) GetTheme() string {
	return c.config.Theme
}

// SetTheme 设置主题
func (c *Config) SetTheme(theme string) {
	c.config.Theme = theme
	c.config.UpdatedAt = time.Now()
}

// GetLanguage 获取语言
func (c *Config) GetLanguage() string {
	return c.config.Language
}

// SetLanguage 设置语言
func (c *Config) SetLanguage(language string) {
	c.config.Language = language
	c.config.UpdatedAt = time.Now()
}

// GetWindowSize 获取窗口大小
func (c *Config) GetWindowSize() (int, int) {
	return c.config.WindowWidth, c.config.WindowHeight
}

// SetWindowSize 设置窗口大小
func (c *Config) SetWindowSize(width, height int) {
	if width < 800 {
		width = 800
	} else if width > 2560 {
		width = 2560
	}

	if height < 600 {
		height = 600
	} else if height > 1440 {
		height = 1440
	}

	c.config.WindowWidth = width
	c.config.WindowHeight = height
	c.config.UpdatedAt = time.Now()
}

// GetWindowPosition 获取窗口位置
func (c *Config) GetWindowPosition() (int, int) {
	return c.config.WindowX, c.config.WindowY
}

// SetWindowPosition 设置窗口位置
func (c *Config) SetWindowPosition(x, y int) {
	c.config.WindowX = x
	c.config.WindowY = y
	c.config.UpdatedAt = time.Now()
}

// GetCustomSetting 获取自定义设置
func (c *Config) GetCustomSetting(key string) string {
	if c.config.CustomSettings == nil {
		c.config.CustomSettings = make(map[string]string)
	}
	return c.config.CustomSettings[key]
}

// SetCustomSetting 设置自定义设置
func (c *Config) SetCustomSetting(key, value string) {
	if c.config.CustomSettings == nil {
		c.config.CustomSettings = make(map[string]string)
	}
	c.config.CustomSettings[key] = value
	c.config.UpdatedAt = time.Now()
}

// RemoveCustomSetting 移除自定义设置
func (c *Config) RemoveCustomSetting(key string) {
	if c.config.CustomSettings != nil {
		delete(c.config.CustomSettings, key)
		c.config.UpdatedAt = time.Now()
	}
}

// ResetToDefaults 重置为默认配置
func (c *Config) ResetToDefaults() {
	// 保留窗口位置和大小
	windowX := c.config.WindowX
	windowY := c.config.WindowY
	windowWidth := c.config.WindowWidth
	windowHeight := c.config.WindowHeight

	// 重置为默认配置
	c.config = c.getDefaultConfig()

	// 恢复窗口设置
	c.config.WindowX = windowX
	c.config.WindowY = windowY
	c.config.WindowWidth = windowWidth
	c.config.WindowHeight = windowHeight
}

// Validate 验证配置
func (c *Config) Validate() []string {
	var errors []string

	if c.config.MaxConcurrency < 1 || c.config.MaxConcurrency > 20 {
		errors = append(errors, "最大并发数必须在1-20之间")
	}

	if c.config.SSHTimeout < 5 || c.config.SSHTimeout > 300 {
		errors = append(errors, "SSH超时时间必须在5-300秒之间")
	}

	if c.config.ScanTimeout < 60 || c.config.ScanTimeout > 3600 {
		errors = append(errors, "扫描超时时间必须在60-3600秒之间")
	}

	if c.config.BackupInterval < 1 || c.config.BackupInterval > 168 {
		errors = append(errors, "备份间隔必须在1-168小时之间")
	}

	if c.config.WindowWidth < 800 || c.config.WindowWidth > 2560 {
		errors = append(errors, "窗口宽度必须在800-2560像素之间")
	}

	if c.config.WindowHeight < 600 || c.config.WindowHeight > 1440 {
		errors = append(errors, "窗口高度必须在600-1440像素之间")
	}

	return errors
}

// GetSupportedThemes 获取支持的主题列表
func (c *Config) GetSupportedThemes() []string {
	return []string{
		"default",
		"dark",
		"light",
	}
}

// GetSupportedLanguages 获取支持的语言列表
func (c *Config) GetSupportedLanguages() []string {
	return []string{
		"zh-CN",
		"en-US",
	}
}

// GetLogLevels 获取支持的日志级别
func (c *Config) GetLogLevels() []string {
	return []string{
		"DEBUG",
		"INFO",
		"WARN",
		"ERROR",
	}
}
