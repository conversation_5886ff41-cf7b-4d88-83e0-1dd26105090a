package core

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"linux-baseline-checker/data"
)

// BaselineChecker 基线检查器
type BaselineChecker struct {
	sshClient *SSHClient
	checks    []BaselineCheck
}

// BaselineCheck 基线检查项
type BaselineCheck struct {
	ID          string
	Name        string
	Category    string
	Description string
	Risk        string
	Solution    string
	Reference   string
	CheckFunc   func(*SSHClient, data.HostInfo) data.BaselineCheckResult
}

// NewBaselineChecker 创建基线检查器
func NewBaselineChecker(sshClient *SSHClient) *BaselineChecker {
	checker := &BaselineChecker{
		sshClient: sshClient,
	}
	
	checker.initializeChecks()
	return checker
}

// GetAllChecks 获取所有检查项
func (bc *BaselineChecker) GetAllChecks() []BaselineCheck {
	return bc.checks
}

// RunCheck 执行单个检查
func (bc *BaselineChecker) RunCheck(host data.HostInfo, checkID string) (*data.BaselineCheckResult, error) {
	for _, check := range bc.checks {
		if check.ID == checkID {
			startTime := time.Now()
			result := check.CheckFunc(bc.sshClient, host)
			result.Duration = time.Since(startTime)
			result.CheckedAt = time.Now()
			return &result, nil
		}
	}
	
	return nil, fmt.Errorf("检查项不存在: %s", checkID)
}

// RunAllChecks 执行所有检查
func (bc *BaselineChecker) RunAllChecks(host data.HostInfo) ([]data.BaselineCheckResult, error) {
	var results []data.BaselineCheckResult
	
	for _, check := range bc.checks {
		startTime := time.Now()
		result := check.CheckFunc(bc.sshClient, host)
		result.ID = check.ID
		result.CheckName = check.Name
		result.Category = check.Category
		result.Description = check.Description
		result.Risk = check.Risk
		result.Solution = check.Solution
		result.Reference = check.Reference
		result.Duration = time.Since(startTime)
		result.CheckedAt = time.Now()
		
		results = append(results, result)
	}
	
	return results, nil
}

// RunCategoryChecks 执行指定类别的检查
func (bc *BaselineChecker) RunCategoryChecks(host data.HostInfo, category string) ([]data.BaselineCheckResult, error) {
	var results []data.BaselineCheckResult
	
	for _, check := range bc.checks {
		if check.Category == category {
			startTime := time.Now()
			result := check.CheckFunc(bc.sshClient, host)
			result.ID = check.ID
			result.CheckName = check.Name
			result.Category = check.Category
			result.Description = check.Description
			result.Risk = check.Risk
			result.Solution = check.Solution
			result.Reference = check.Reference
			result.Duration = time.Since(startTime)
			result.CheckedAt = time.Now()
			
			results = append(results, result)
		}
	}
	
	return results, nil
}

// initializeChecks 初始化所有检查项
func (bc *BaselineChecker) initializeChecks() {
	bc.checks = []BaselineCheck{
		// 账户安全检查
		{
			ID:          "ACC001",
			Name:        "密码策略检查",
			Category:    "账户安全",
			Description: "检查系统密码复杂度策略配置",
			Risk:        "中等",
			Solution:    "配置强密码策略，设置最小长度、复杂度要求",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkPasswordPolicy,
		},
		{
			ID:          "ACC002",
			Name:        "root账户检查",
			Category:    "账户安全",
			Description: "检查root账户安全配置",
			Risk:        "高",
			Solution:    "禁用root远程登录，使用sudo管理权限",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkRootAccount,
		},
		{
			ID:          "ACC003",
			Name:        "空密码账户检查",
			Category:    "账户安全",
			Description: "检查是否存在空密码账户",
			Risk:        "严重",
			Solution:    "为所有账户设置强密码或禁用账户",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkEmptyPasswordAccounts,
		},
		
		// SSH安全检查
		{
			ID:          "SSH001",
			Name:        "SSH配置检查",
			Category:    "SSH安全",
			Description: "检查SSH服务安全配置",
			Risk:        "高",
			Solution:    "配置SSH安全参数，禁用不安全选项",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkSSHConfig,
		},
		{
			ID:          "SSH002",
			Name:        "SSH协议版本检查",
			Category:    "SSH安全",
			Description: "检查SSH协议版本",
			Risk:        "中等",
			Solution:    "使用SSH协议版本2",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkSSHProtocol,
		},
		
		// 网络安全检查
		{
			ID:          "NET001",
			Name:        "防火墙状态检查",
			Category:    "网络安全",
			Description: "检查防火墙是否启用",
			Risk:        "高",
			Solution:    "启用并正确配置防火墙",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkFirewallStatus,
		},
		{
			ID:          "NET002",
			Name:        "网络服务检查",
			Category:    "网络安全",
			Description: "检查不必要的网络服务",
			Risk:        "中等",
			Solution:    "关闭不必要的网络服务",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkNetworkServices,
		},
		
		// 系统安全检查
		{
			ID:          "SYS001",
			Name:        "系统更新检查",
			Category:    "系统安全",
			Description: "检查系统更新状态",
			Risk:        "中等",
			Solution:    "及时安装系统安全更新",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkSystemUpdates,
		},
		{
			ID:          "SYS002",
			Name:        "内核参数检查",
			Category:    "系统安全",
			Description: "检查安全相关的内核参数",
			Risk:        "中等",
			Solution:    "配置安全的内核参数",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkKernelParameters,
		},
		
		// 文件系统安全检查
		{
			ID:          "FS001",
			Name:        "关键文件权限检查",
			Category:    "文件系统",
			Description: "检查关键系统文件权限",
			Risk:        "高",
			Solution:    "设置正确的文件权限",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkCriticalFilePermissions,
		},
		{
			ID:          "FS002",
			Name:        "SUID/SGID文件检查",
			Category:    "文件系统",
			Description: "检查SUID/SGID文件",
			Risk:        "中等",
			Solution:    "审查并移除不必要的SUID/SGID权限",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkSUIDSGIDFiles,
		},
		
		// 日志审计检查
		{
			ID:          "LOG001",
			Name:        "日志配置检查",
			Category:    "日志审计",
			Description: "检查系统日志配置",
			Risk:        "中等",
			Solution:    "配置完整的日志记录",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkLogConfiguration,
		},
		{
			ID:          "LOG002",
			Name:        "审计服务检查",
			Category:    "日志审计",
			Description: "检查审计服务状态",
			Risk:        "中等",
			Solution:    "启用并配置审计服务",
			Reference:   "CIS Linux Benchmark",
			CheckFunc:   bc.checkAuditService,
		},
	}
}

// 具体检查函数实现

// checkPasswordPolicy 检查密码策略
func (bc *BaselineChecker) checkPasswordPolicy(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "grep -E '^(PASS_MIN_LEN|PASS_MAX_DAYS|PASS_MIN_DAYS|PASS_WARN_AGE)' /etc/login.defs",
	}
	
	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}
	
	result.Output = output
	
	// 分析密码策略配置
	lines := strings.Split(output, "\n")
	policies := make(map[string]string)
	
	for _, line := range lines {
		if strings.TrimSpace(line) == "" || strings.HasPrefix(strings.TrimSpace(line), "#") {
			continue
		}
		
		parts := strings.Fields(line)
		if len(parts) >= 2 {
			policies[parts[0]] = parts[1]
		}
	}
	
	score := 0
	issues := []string{}
	
	// 检查最小密码长度
	if minLen, exists := policies["PASS_MIN_LEN"]; exists {
		if len, err := strconv.Atoi(minLen); err == nil && len >= 8 {
			score += 25
		} else {
			issues = append(issues, "密码最小长度应设置为8位或以上")
		}
	} else {
		issues = append(issues, "未配置密码最小长度")
	}
	
	// 检查密码最大有效期
	if maxDays, exists := policies["PASS_MAX_DAYS"]; exists {
		if days, err := strconv.Atoi(maxDays); err == nil && days <= 90 && days > 0 {
			score += 25
		} else {
			issues = append(issues, "密码最大有效期应设置为90天以内")
		}
	} else {
		issues = append(issues, "未配置密码最大有效期")
	}
	
	// 检查密码最小间隔
	if minDays, exists := policies["PASS_MIN_DAYS"]; exists {
		if days, err := strconv.Atoi(minDays); err == nil && days >= 1 {
			score += 25
		} else {
			issues = append(issues, "密码最小更改间隔应设置为1天或以上")
		}
	} else {
		issues = append(issues, "未配置密码最小更改间隔")
	}
	
	// 检查密码过期警告
	if warnAge, exists := policies["PASS_WARN_AGE"]; exists {
		if days, err := strconv.Atoi(warnAge); err == nil && days >= 7 {
			score += 25
		} else {
			issues = append(issues, "密码过期警告应设置为7天或以上")
		}
	} else {
		issues = append(issues, "未配置密码过期警告")
	}
	
	result.Score = score
	
	if score >= 80 {
		result.Status = "通过"
		result.Details = "密码策略配置良好"
	} else if score >= 50 {
		result.Status = "警告"
		result.Details = fmt.Sprintf("密码策略存在问题: %s", strings.Join(issues, "; "))
	} else {
		result.Status = "失败"
		result.Details = fmt.Sprintf("密码策略配置不当: %s", strings.Join(issues, "; "))
	}
	
	return result
}

// checkRootAccount 检查root账户
func (bc *BaselineChecker) checkRootAccount(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "grep '^PermitRootLogin' /etc/ssh/sshd_config",
	}
	
	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}
	
	result.Output = output
	
	if strings.Contains(strings.ToLower(output), "no") {
		result.Status = "通过"
		result.Score = 100
		result.Details = "root远程登录已禁用"
	} else {
		result.Status = "失败"
		result.Score = 0
		result.Details = "root远程登录未禁用，存在安全风险"
	}
	
	return result
}

// checkEmptyPasswordAccounts 检查空密码账户
func (bc *BaselineChecker) checkEmptyPasswordAccounts(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "awk -F: '($2 == \"\") {print $1}' /etc/shadow",
	}
	
	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}
	
	result.Output = output
	
	if strings.TrimSpace(output) == "" {
		result.Status = "通过"
		result.Score = 100
		result.Details = "未发现空密码账户"
	} else {
		result.Status = "失败"
		result.Score = 0
		result.Details = fmt.Sprintf("发现空密码账户: %s", strings.ReplaceAll(output, "\n", ", "))
	}
	
	return result
}

// checkSSHConfig 检查SSH配置
func (bc *BaselineChecker) checkSSHConfig(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "grep -E '^(Protocol|PermitRootLogin|PasswordAuthentication|PermitEmptyPasswords|X11Forwarding)' /etc/ssh/sshd_config",
	}

	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	score := 0
	issues := []string{}

	lines := strings.Split(output, "\n")
	configs := make(map[string]string)

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}
		parts := strings.Fields(line)
		if len(parts) >= 2 {
			configs[parts[0]] = strings.ToLower(parts[1])
		}
	}

	// 检查各项配置
	if val, exists := configs["PermitRootLogin"]; exists && val == "no" {
		score += 30
	} else {
		issues = append(issues, "应禁用root登录")
	}

	if val, exists := configs["PermitEmptyPasswords"]; exists && val == "no" {
		score += 25
	} else {
		issues = append(issues, "应禁用空密码登录")
	}

	if val, exists := configs["X11Forwarding"]; exists && val == "no" {
		score += 20
	} else {
		issues = append(issues, "应禁用X11转发")
	}

	if val, exists := configs["PasswordAuthentication"]; exists && val == "no" {
		score += 25
	} else {
		score += 10 // 密码认证可以启用，但密钥认证更安全
	}

	result.Score = score

	if score >= 80 {
		result.Status = "通过"
		result.Details = "SSH配置安全"
	} else if score >= 50 {
		result.Status = "警告"
		result.Details = fmt.Sprintf("SSH配置存在问题: %s", strings.Join(issues, "; "))
	} else {
		result.Status = "失败"
		result.Details = fmt.Sprintf("SSH配置不安全: %s", strings.Join(issues, "; "))
	}

	return result
}

// checkSSHProtocol 检查SSH协议版本
func (bc *BaselineChecker) checkSSHProtocol(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "ssh -V 2>&1 | head -1",
	}

	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	if strings.Contains(strings.ToLower(output), "openssh") {
		result.Status = "通过"
		result.Score = 100
		result.Details = "使用OpenSSH，协议版本安全"
	} else {
		result.Status = "警告"
		result.Score = 70
		result.Details = "建议使用OpenSSH"
	}

	return result
}

// checkFirewallStatus 检查防火墙状态
func (bc *BaselineChecker) checkFirewallStatus(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "ufw status || iptables -L | head -10",
	}

	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	if strings.Contains(strings.ToLower(output), "active") ||
	   strings.Contains(output, "Chain INPUT") {
		result.Status = "通过"
		result.Score = 100
		result.Details = "防火墙已启用"
	} else {
		result.Status = "失败"
		result.Score = 0
		result.Details = "防火墙未启用或配置不当"
	}

	return result
}

// checkNetworkServices 检查网络服务
func (bc *BaselineChecker) checkNetworkServices(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "netstat -tuln | grep LISTEN",
	}

	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	lines := strings.Split(output, "\n")
	serviceCount := 0
	riskyServices := []string{}

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}
		serviceCount++

		// 检查高风险端口
		if strings.Contains(line, ":21 ") { // FTP
			riskyServices = append(riskyServices, "FTP(21)")
		}
		if strings.Contains(line, ":23 ") { // Telnet
			riskyServices = append(riskyServices, "Telnet(23)")
		}
		if strings.Contains(line, ":135 ") { // RPC
			riskyServices = append(riskyServices, "RPC(135)")
		}
	}

	score := 100
	if len(riskyServices) > 0 {
		score -= len(riskyServices) * 20
		if score < 0 {
			score = 0
		}
	}

	result.Score = score

	if len(riskyServices) == 0 {
		result.Status = "通过"
		result.Details = fmt.Sprintf("发现%d个监听服务，未发现高风险服务", serviceCount)
	} else {
		result.Status = "警告"
		result.Details = fmt.Sprintf("发现高风险服务: %s", strings.Join(riskyServices, ", "))
	}

	return result
}

// checkSystemUpdates 检查系统更新
func (bc *BaselineChecker) checkSystemUpdates(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "apt list --upgradable 2>/dev/null | wc -l || yum check-update 2>/dev/null | wc -l",
	}

	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	if updateCount, err := strconv.Atoi(strings.TrimSpace(output)); err == nil {
		if updateCount <= 1 { // 通常会有一行标题
			result.Status = "通过"
			result.Score = 100
			result.Details = "系统已是最新版本"
		} else if updateCount <= 10 {
			result.Status = "警告"
			result.Score = 70
			result.Details = fmt.Sprintf("有%d个可用更新", updateCount-1)
		} else {
			result.Status = "失败"
			result.Score = 30
			result.Details = fmt.Sprintf("有%d个可用更新，建议及时更新", updateCount-1)
		}
	} else {
		result.Status = "失败"
		result.Score = 0
		result.Details = "无法检查系统更新状态"
	}

	return result
}

// checkKernelParameters 检查内核参数
func (bc *BaselineChecker) checkKernelParameters(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "sysctl net.ipv4.ip_forward net.ipv4.conf.all.send_redirects net.ipv4.conf.all.accept_redirects",
	}

	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	score := 0
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		if strings.Contains(line, "net.ipv4.ip_forward = 0") {
			score += 40
		}
		if strings.Contains(line, "net.ipv4.conf.all.send_redirects = 0") {
			score += 30
		}
		if strings.Contains(line, "net.ipv4.conf.all.accept_redirects = 0") {
			score += 30
		}
	}

	result.Score = score

	if score >= 80 {
		result.Status = "通过"
		result.Details = "内核安全参数配置良好"
	} else {
		result.Status = "警告"
		result.Details = "部分内核安全参数需要调整"
	}

	return result
}

// checkCriticalFilePermissions 检查关键文件权限
func (bc *BaselineChecker) checkCriticalFilePermissions(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "stat -c '%a %n' /etc/passwd /etc/shadow /etc/group /etc/gshadow 2>/dev/null",
	}

	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	score := 0
	issues := []string{}
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) >= 2 {
			perm := parts[0]
			file := parts[1]

			switch file {
			case "/etc/passwd":
				if perm == "644" {
					score += 25
				} else {
					issues = append(issues, fmt.Sprintf("/etc/passwd权限应为644，当前为%s", perm))
				}
			case "/etc/shadow":
				if perm == "640" || perm == "600" {
					score += 25
				} else {
					issues = append(issues, fmt.Sprintf("/etc/shadow权限应为640或600，当前为%s", perm))
				}
			case "/etc/group":
				if perm == "644" {
					score += 25
				} else {
					issues = append(issues, fmt.Sprintf("/etc/group权限应为644，当前为%s", perm))
				}
			case "/etc/gshadow":
				if perm == "640" || perm == "600" {
					score += 25
				} else {
					issues = append(issues, fmt.Sprintf("/etc/gshadow权限应为640或600，当前为%s", perm))
				}
			}
		}
	}

	result.Score = score

	if score >= 80 {
		result.Status = "通过"
		result.Details = "关键文件权限配置正确"
	} else {
		result.Status = "失败"
		result.Details = fmt.Sprintf("文件权限问题: %s", strings.Join(issues, "; "))
	}

	return result
}

// checkSUIDSGIDFiles 检查SUID/SGID文件
func (bc *BaselineChecker) checkSUIDSGIDFiles(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "find /usr /bin /sbin -perm /6000 -type f 2>/dev/null | wc -l",
	}

	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	if count, err := strconv.Atoi(strings.TrimSpace(output)); err == nil {
		if count <= 20 {
			result.Status = "通过"
			result.Score = 100
			result.Details = fmt.Sprintf("发现%d个SUID/SGID文件，数量合理", count)
		} else if count <= 50 {
			result.Status = "警告"
			result.Score = 70
			result.Details = fmt.Sprintf("发现%d个SUID/SGID文件，建议审查", count)
		} else {
			result.Status = "失败"
			result.Score = 30
			result.Details = fmt.Sprintf("发现%d个SUID/SGID文件，数量过多", count)
		}
	} else {
		result.Status = "失败"
		result.Score = 0
		result.Details = "无法检查SUID/SGID文件"
	}

	return result
}

// checkLogConfiguration 检查日志配置
func (bc *BaselineChecker) checkLogConfiguration(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "systemctl is-active rsyslog || systemctl is-active syslog-ng",
	}

	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	if strings.Contains(strings.ToLower(output), "active") {
		result.Status = "通过"
		result.Score = 100
		result.Details = "日志服务正在运行"
	} else {
		result.Status = "失败"
		result.Score = 0
		result.Details = "日志服务未运行"
	}

	return result
}

// checkAuditService 检查审计服务
func (bc *BaselineChecker) checkAuditService(client *SSHClient, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		Command: "systemctl is-active auditd",
	}

	output, err := client.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	if strings.Contains(strings.ToLower(output), "active") {
		result.Status = "通过"
		result.Score = 100
		result.Details = "审计服务正在运行"
	} else {
		result.Status = "警告"
		result.Score = 50
		result.Details = "审计服务未运行，建议启用"
	}

	return result
}
