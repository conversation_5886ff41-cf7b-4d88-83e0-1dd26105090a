package core

import (
	"context"
	"fmt"
	"sync"
	"time"

	"linux-baseline-checker/data"
)

// Scanner 扫描引擎
type Scanner struct {
	storage        *data.Storage
	sshTimeout     time.Duration
	scanTimeout    time.Duration
	maxConcurrency int
	progressChan   chan data.ScanProgress
	mu             sync.RWMutex
	activeScans    map[string]*ScanContext
}

// ScanContext 扫描上下文
type ScanContext struct {
	ID        string
	Status    string
	Progress  int
	Cancel    context.CancelFunc
	StartTime time.Time
}

// NewScanner 创建扫描引擎
func NewScanner(storage *data.Storage, sshTimeout, scanTimeout time.Duration, maxConcurrency int) *Scanner {
	return &Scanner{
		storage:        storage,
		sshTimeout:     sshTimeout,
		scanTimeout:    scanTimeout,
		maxConcurrency: maxConcurrency,
		progressChan:   make(chan data.ScanProgress, 100),
		activeScans:    make(map[string]*ScanContext),
	}
}

// GetProgressChannel 获取进度通道
func (s *Scanner) GetProgressChannel() <-chan data.ScanProgress {
	return s.progressChan
}

// ScanSingleHost 扫描单个主机
func (s *Scanner) ScanSingleHost(host data.HostInfo) (*data.ScanResult, error) {
	ctx, cancel := context.WithTimeout(context.Background(), s.scanTimeout)
	defer cancel()

	scanID := fmt.Sprintf("single_%s_%d", host.ID, time.Now().Unix())

	// 注册扫描上下文
	s.mu.Lock()
	s.activeScans[scanID] = &ScanContext{
		ID:        scanID,
		Status:    "运行中",
		Progress:  0,
		Cancel:    cancel,
		StartTime: time.Now(),
	}
	s.mu.Unlock()

	defer func() {
		s.mu.Lock()
		delete(s.activeScans, scanID)
		s.mu.Unlock()
	}()

	// 发送开始进度
	s.sendProgress(data.ScanProgress{
		HostID:   host.ID,
		HostName: host.Name,
		Status:   "开始扫描",
		Progress: 0,
		Message:  "正在初始化扫描...",
	})

	result := &data.ScanResult{
		ID:        scanID,
		HostID:    host.ID,
		HostName:  host.Name,
		Host:      host.Host,
		StartTime: time.Now(),
		Metadata:  make(map[string]string),
	}

	// 创建SSH客户端
	sshClient := NewSSHClient(host, s.sshTimeout)

	// 测试连接
	s.sendProgress(data.ScanProgress{
		HostID:       host.ID,
		HostName:     host.Name,
		Status:       "连接测试",
		Progress:     10,
		CurrentCheck: "SSH连接测试",
		Message:      "正在测试SSH连接...",
	})

	if err := sshClient.TestConnection(host); err != nil {
		result.Status = "失败"
		result.Error = fmt.Sprintf("SSH连接失败: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)

		s.sendProgress(data.ScanProgress{
			HostID:   host.ID,
			HostName: host.Name,
			Status:   "失败",
			Progress: 100,
			Error:    result.Error,
		})

		return result, err
	}

	// 获取系统信息
	s.sendProgress(data.ScanProgress{
		HostID:       host.ID,
		HostName:     host.Name,
		Status:       "获取系统信息",
		Progress:     20,
		CurrentCheck: "系统信息收集",
		Message:      "正在收集系统信息...",
	})

	systemInfo, err := sshClient.GetSystemInfo(host)
	if err != nil {
		result.Status = "部分成功"
		result.Error = fmt.Sprintf("获取系统信息失败: %v", err)
	} else {
		result.SystemInfo = systemInfo
	}

	// 执行基线检查
	s.sendProgress(data.ScanProgress{
		HostID:       host.ID,
		HostName:     host.Name,
		Status:       "执行基线检查",
		Progress:     30,
		CurrentCheck: "基线安全检查",
		Message:      "正在执行安全基线检查...",
	})

	checker := NewBaselineChecker(sshClient)
	checks := checker.GetAllChecks()

	result.TotalChecks = len(checks)
	checkResults := make([]data.BaselineCheckResult, 0, len(checks))

	for i, check := range checks {
		// 检查是否被取消
		select {
		case <-ctx.Done():
			result.Status = "已取消"
			result.Error = "扫描被用户取消"
			result.EndTime = time.Now()
			result.Duration = time.Since(result.StartTime)
			return result, ctx.Err()
		default:
		}

		// 更新进度
		progress := 30 + (i*60)/len(checks)
		s.sendProgress(data.ScanProgress{
			HostID:       host.ID,
			HostName:     host.Name,
			Status:       "执行检查",
			Progress:     progress,
			CurrentCheck: check.Name,
			Message:      fmt.Sprintf("正在执行: %s", check.Name),
		})

		// 执行检查
		checkResult, err := checker.RunCheck(host, check.ID)
		if err != nil {
			// 记录错误但继续其他检查
			checkResult = &data.BaselineCheckResult{
				ID:          check.ID,
				CheckName:   check.Name,
				Category:    check.Category,
				Status:      "失败",
				Score:       0,
				Risk:        check.Risk,
				Description: check.Description,
				Error:       err.Error(),
				CheckedAt:   time.Now(),
			}
		}

		checkResults = append(checkResults, *checkResult)

		// 更新统计
		switch checkResult.Status {
		case "通过":
			result.PassedChecks++
		case "失败":
			result.FailedChecks++
		case "警告":
			result.WarningChecks++
		default:
			result.SkippedChecks++
		}

		result.TotalScore += checkResult.Score
	}

	result.CheckResults = checkResults
	result.MaxScore = len(checks) * 100 // 假设每个检查最高100分

	// 确定最终状态
	if result.Error == "" {
		if result.FailedChecks == 0 {
			result.Status = "成功"
		} else {
			result.Status = "部分成功"
		}
	}

	result.EndTime = time.Now()
	result.Duration = time.Since(result.StartTime)
	result.Progress = 100

	// 发送完成进度
	s.sendProgress(data.ScanProgress{
		HostID:   host.ID,
		HostName: host.Name,
		Status:   "完成",
		Progress: 100,
		Message:  fmt.Sprintf("扫描完成，通过: %d, 失败: %d", result.PassedChecks, result.FailedChecks),
	})

	// 保存结果
	if err := s.storage.SaveScanResult(*result); err != nil {
		// 记录错误但不影响返回结果
		fmt.Printf("保存扫描结果失败: %v\n", err)
	}

	return result, nil
}

// ScanHostGroup 批量扫描主机组
func (s *Scanner) ScanHostGroup(group data.HostGroup, concurrency int) (*data.BatchScanResult, error) {
	if concurrency <= 0 || concurrency > s.maxConcurrency {
		concurrency = s.maxConcurrency
	}

	batchResult := &data.BatchScanResult{
		ID:          fmt.Sprintf("batch_%s_%d", group.ID, time.Now().Unix()),
		GroupID:     group.ID,
		GroupName:   group.Name,
		Status:      "运行中",
		TotalHosts:  len(group.Hosts),
		StartTime:   time.Now(),
		Concurrency: concurrency,
		Results:     make([]data.ScanResult, 0, len(group.Hosts)),
		Metadata:    make(map[string]string),
	}

	if len(group.Hosts) == 0 {
		batchResult.Status = "完成"
		batchResult.EndTime = time.Now()
		batchResult.Duration = time.Since(batchResult.StartTime)
		return batchResult, nil
	}

	// 创建工作池
	hostChan := make(chan data.HostInfo, len(group.Hosts))
	resultChan := make(chan *data.ScanResult, len(group.Hosts))

	// 发送主机到通道
	for _, host := range group.Hosts {
		hostChan <- host
	}
	close(hostChan)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for host := range hostChan {
				result, err := s.ScanSingleHost(host)
				if err != nil {
					// 创建失败结果
					result = &data.ScanResult{
						ID:        fmt.Sprintf("failed_%s_%d", host.ID, time.Now().Unix()),
						HostID:    host.ID,
						HostName:  host.Name,
						Host:      host.Host,
						Status:    "失败",
						Error:     err.Error(),
						StartTime: time.Now(),
						EndTime:   time.Now(),
					}
					result.Duration = time.Since(result.StartTime)
				}
				resultChan <- result
			}
		}()
	}

	// 等待所有工作完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	for result := range resultChan {
		batchResult.Results = append(batchResult.Results, *result)

		if result.Status == "失败" {
			batchResult.FailedHosts++
		} else {
			batchResult.CompletedHosts++
		}

		// 更新批量扫描进度
		batchResult.Progress = (batchResult.CompletedHosts + batchResult.FailedHosts) * 100 / batchResult.TotalHosts
	}

	// 确定最终状态
	if batchResult.FailedHosts == 0 {
		batchResult.Status = "成功"
	} else if batchResult.CompletedHosts > 0 {
		batchResult.Status = "部分成功"
	} else {
		batchResult.Status = "失败"
	}

	batchResult.EndTime = time.Now()
	batchResult.Duration = time.Since(batchResult.StartTime)
	batchResult.Progress = 100

	// 保存批量扫描结果
	if err := s.storage.SaveBatchScanResult(*batchResult); err != nil {
		fmt.Printf("保存批量扫描结果失败: %v\n", err)
	}

	return batchResult, nil
}

// CancelScan 取消扫描
func (s *Scanner) CancelScan(scanID string) error {
	s.mu.RLock()
	scanCtx, exists := s.activeScans[scanID]
	s.mu.RUnlock()

	if !exists {
		return fmt.Errorf("扫描不存在: %s", scanID)
	}

	scanCtx.Cancel()
	scanCtx.Status = "已取消"

	return nil
}

// GetActiveScan 获取活动扫描
func (s *Scanner) GetActiveScans() map[string]*ScanContext {
	s.mu.RLock()
	defer s.mu.RUnlock()

	result := make(map[string]*ScanContext)
	for k, v := range s.activeScans {
		result[k] = v
	}

	return result
}

// sendProgress 发送进度更新
func (s *Scanner) sendProgress(progress data.ScanProgress) {
	select {
	case s.progressChan <- progress:
	default:
		// 通道满了，跳过这次更新
	}
}
