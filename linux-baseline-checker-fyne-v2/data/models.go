package data

import (
	"time"
)

// HostInfo 主机信息
type HostInfo struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Host        string    `json:"host"`
	Port        string    `json:"port"`
	Username    string    `json:"username"`
	Password    string    `json:"password"`
	Status      string    `json:"status"`
	LastScan    time.Time `json:"lastScan"`
	Description string    `json:"description"`
	Tags        []string  `json:"tags"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// HostGroup 主机组
type HostGroup struct {
	ID          string     `json:"id"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Hosts       []HostInfo `json:"hosts"`
	Tags        []string   `json:"tags"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   time.Time  `json:"updatedAt"`
}

// SystemInfo 系统信息
type SystemInfo struct {
	Hostname     string            `json:"hostname"`
	OS           string            `json:"os"`
	OSVersion    string            `json:"osVersion"`
	Kernel       string            `json:"kernel"`
	Architecture string            `json:"architecture"`
	Uptime       string            `json:"uptime"`
	CPUInfo      string            `json:"cpuInfo"`
	CPUCores     int               `json:"cpuCores"`
	MemoryTotal  string            `json:"memoryTotal"`
	MemoryUsed   string            `json:"memoryUsed"`
	DiskInfo     []DiskInfo        `json:"diskInfo"`
	NetworkInfo  []NetworkInfo     `json:"networkInfo"`
	Services     []ServiceInfo     `json:"services"`
	Users        []UserInfo        `json:"users"`
	Processes    int               `json:"processes"`
	LoadAverage  string            `json:"loadAverage"`
	Environment  map[string]string `json:"environment"`
	CollectedAt  time.Time         `json:"collectedAt"`
}

// DiskInfo 磁盘信息
type DiskInfo struct {
	Device     string `json:"device"`
	MountPoint string `json:"mountPoint"`
	FileSystem string `json:"fileSystem"`
	Size       string `json:"size"`
	Used       string `json:"used"`
	Available  string `json:"available"`
	UsePercent string `json:"usePercent"`
}

// NetworkInfo 网络信息
type NetworkInfo struct {
	Interface string `json:"interface"`
	IPAddress string `json:"ipAddress"`
	Netmask   string `json:"netmask"`
	Status    string `json:"status"`
}

// ServiceInfo 服务信息
type ServiceInfo struct {
	Name   string `json:"name"`
	Status string `json:"status"`
	Port   string `json:"port"`
}

// UserInfo 用户信息
type UserInfo struct {
	Username string `json:"username"`
	UID      string `json:"uid"`
	GID      string `json:"gid"`
	Home     string `json:"home"`
	Shell    string `json:"shell"`
	LastLogin string `json:"lastLogin"`
}

// BaselineCheckResult 基线检查结果
type BaselineCheckResult struct {
	ID          string            `json:"id"`
	CheckName   string            `json:"checkName"`
	Category    string            `json:"category"`
	Status      string            `json:"status"` // 通过、失败、警告、跳过
	Score       int               `json:"score"`  // 0-100分
	Risk        string            `json:"risk"`   // 低、中、高、严重
	Description string            `json:"description"`
	Details     string            `json:"details"`
	Solution    string            `json:"solution"`
	Reference   string            `json:"reference"`
	Command     string            `json:"command"`
	Output      string            `json:"output"`
	Error       string            `json:"error"`
	Duration    time.Duration     `json:"duration"`
	Metadata    map[string]string `json:"metadata"`
	CheckedAt   time.Time         `json:"checkedAt"`
}

// ScanResult 扫描结果
type ScanResult struct {
	ID           string                    `json:"id"`
	HostID       string                    `json:"hostId"`
	HostName     string                    `json:"hostName"`
	Host         string                    `json:"host"`
	Status       string                    `json:"status"` // 成功、失败、部分成功
	Progress     int                       `json:"progress"` // 0-100
	TotalChecks  int                       `json:"totalChecks"`
	PassedChecks int                       `json:"passedChecks"`
	FailedChecks int                       `json:"failedChecks"`
	WarningChecks int                      `json:"warningChecks"`
	SkippedChecks int                      `json:"skippedChecks"`
	TotalScore   int                       `json:"totalScore"`
	MaxScore     int                       `json:"maxScore"`
	CheckResults []BaselineCheckResult     `json:"checkResults"`
	SystemInfo   *SystemInfo               `json:"systemInfo"`
	StartTime    time.Time                 `json:"startTime"`
	EndTime      time.Time                 `json:"endTime"`
	Duration     time.Duration             `json:"duration"`
	Error        string                    `json:"error"`
	Metadata     map[string]string         `json:"metadata"`
}

// BatchScanResult 批量扫描结果
type BatchScanResult struct {
	ID          string            `json:"id"`
	GroupID     string            `json:"groupId"`
	GroupName   string            `json:"groupName"`
	Status      string            `json:"status"`
	Progress    int               `json:"progress"`
	TotalHosts  int               `json:"totalHosts"`
	CompletedHosts int            `json:"completedHosts"`
	FailedHosts int               `json:"failedHosts"`
	Results     []ScanResult      `json:"results"`
	StartTime   time.Time         `json:"startTime"`
	EndTime     time.Time         `json:"endTime"`
	Duration    time.Duration     `json:"duration"`
	Concurrency int               `json:"concurrency"`
	Metadata    map[string]string `json:"metadata"`
}

// ScanProgress 扫描进度
type ScanProgress struct {
	HostID      string `json:"hostId"`
	HostName    string `json:"hostName"`
	Status      string `json:"status"`
	Progress    int    `json:"progress"`
	CurrentCheck string `json:"currentCheck"`
	Message     string `json:"message"`
	Error       string `json:"error"`
}

// AppConfig 应用配置
type AppConfig struct {
	Version         string            `json:"version"`
	DataDir         string            `json:"dataDir"`
	LogLevel        string            `json:"logLevel"`
	MaxConcurrency  int               `json:"maxConcurrency"`
	SSHTimeout      int               `json:"sshTimeout"`
	ScanTimeout     int               `json:"scanTimeout"`
	AutoSave        bool              `json:"autoSave"`
	BackupEnabled   bool              `json:"backupEnabled"`
	BackupInterval  int               `json:"backupInterval"`
	Theme           string            `json:"theme"`
	Language        string            `json:"language"`
	WindowWidth     int               `json:"windowWidth"`
	WindowHeight    int               `json:"windowHeight"`
	WindowX         int               `json:"windowX"`
	WindowY         int               `json:"windowY"`
	CustomSettings  map[string]string `json:"customSettings"`
	UpdatedAt       time.Time         `json:"updatedAt"`
}

// Statistics 统计信息
type Statistics struct {
	TotalHosts       int       `json:"totalHosts"`
	TotalGroups      int       `json:"totalGroups"`
	TotalScans       int       `json:"totalScans"`
	LastScanTime     time.Time `json:"lastScanTime"`
	TotalChecks      int       `json:"totalChecks"`
	PassedChecks     int       `json:"passedChecks"`
	FailedChecks     int       `json:"failedChecks"`
	WarningChecks    int       `json:"warningChecks"`
	HighRiskIssues   int       `json:"highRiskIssues"`
	MediumRiskIssues int       `json:"mediumRiskIssues"`
	LowRiskIssues    int       `json:"lowRiskIssues"`
	AverageScore     float64   `json:"averageScore"`
	UpdatedAt        time.Time `json:"updatedAt"`
}
