package main

import (
	"fmt"
	"time"

	"linux-baseline-checker/core"
	"linux-baseline-checker/data"
)

// 简单的连接测试程序，用于验证SSH连接功能
func main() {
	fmt.Println("SSH连接测试程序")
	fmt.Println("================")

	// 创建测试主机信息
	host := data.HostInfo{
		ID:       "test-host",
		Name:     "测试主机",
		Host:     "127.0.0.1", // 本地测试
		Port:     "22",
		Username: "root",
		Password: "password", // 请替换为实际密码
	}

	fmt.Printf("测试主机: %s (%s:%s)\n", host.Name, host.Host, host.Port)
	fmt.Printf("用户名: %s\n", host.Username)
	fmt.Println("开始连接测试...")

	// 创建SSH客户端
	sshClient := core.NewSSHClient(host, 30*time.Second)

	// 测试连接
	start := time.Now()
	err := sshClient.TestConnection(host)
	duration := time.Since(start)

	fmt.Printf("测试耗时: %v\n", duration)

	if err != nil {
		fmt.Printf("❌ 连接失败: %v\n", err)
	} else {
		fmt.Println("✅ 连接成功!")
		
		// 如果连接成功，尝试执行一个简单命令
		fmt.Println("尝试执行测试命令...")
		output, err := sshClient.ExecuteCommand(host, "whoami")
		if err != nil {
			fmt.Printf("❌ 命令执行失败: %v\n", err)
		} else {
			fmt.Printf("✅ 命令执行成功，输出: %s\n", output)
		}
	}

	fmt.Println("测试完成")
}
