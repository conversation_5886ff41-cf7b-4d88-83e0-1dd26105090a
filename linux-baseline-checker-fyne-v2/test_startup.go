package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"linux-baseline-checker/config"
	"linux-baseline-checker/core"
	"linux-baseline-checker/data"
	"linux-baseline-checker/ui"
)

// 简单的启动测试，用于检查程序是否能正常初始化
func main() {
	fmt.Println("=== Linux基线检查工具启动测试 ===")
	
	// 测试数据存储初始化
	fmt.Print("1. 测试数据存储初始化... ")
	storage := data.NewStorage("")
	if storage == nil {
		log.Fatal("数据存储初始化失败")
	}
	fmt.Println("✓")

	// 测试配置管理器初始化
	fmt.Print("2. 测试配置管理器初始化... ")
	cfg := config.NewConfig(storage)
	if cfg == nil {
		log.Fatal("配置管理器初始化失败")
	}
	fmt.Println("✓")

	// 测试扫描器初始化
	fmt.Print("3. 测试扫描器初始化... ")
	scanner := core.NewScanner(
		storage,
		cfg.GetSSHTimeout(),
		cfg.GetScanTimeout(),
		cfg.GetMaxConcurrency(),
	)
	if scanner == nil {
		log.Fatal("扫描器初始化失败")
	}
	fmt.Println("✓")

	// 测试UI组件初始化（不显示窗口）
	fmt.Print("4. 测试UI组件初始化... ")
	
	// 创建一个简单的状态更新函数
	updateStatus := func(message string) {
		fmt.Printf("状态: %s\n", message)
	}

	// 测试主机管理选项卡
	hostTab := ui.NewHostTab(storage, scanner, updateStatus)
	if hostTab == nil {
		log.Fatal("主机管理选项卡初始化失败")
	}

	// 测试扫描选项卡
	scanTab := ui.NewScanTab(storage, scanner, updateStatus)
	if scanTab == nil {
		log.Fatal("扫描选项卡初始化失败")
	}

	// 测试报告选项卡
	reportTab := ui.NewReportTab(storage, updateStatus)
	if reportTab == nil {
		log.Fatal("报告选项卡初始化失败")
	}
	
	fmt.Println("✓")

	// 测试数据加载
	fmt.Print("5. 测试数据加载... ")
	hosts, err := storage.LoadHosts()
	if err != nil {
		fmt.Printf("警告: 加载主机数据失败: %v\n", err)
	} else {
		fmt.Printf("✓ (加载了 %d 台主机)\n", len(hosts))
	}

	groups, err := storage.LoadHostGroups()
	if err != nil {
		fmt.Printf("警告: 加载主机组数据失败: %v\n", err)
	} else {
		fmt.Printf("✓ (加载了 %d 个主机组)\n", len(groups))
	}

	// 测试SSH客户端创建
	fmt.Print("6. 测试SSH客户端创建... ")
	testHost := data.HostInfo{
		ID:       "test",
		Name:     "测试主机",
		Host:     "127.0.0.1",
		Port:     "22",
		Username: "test",
		Password: "test",
	}
	
	sshClient := core.NewSSHClient(testHost, 10*time.Second)
	if sshClient == nil {
		log.Fatal("SSH客户端创建失败")
	}
	fmt.Println("✓")

	fmt.Println("\n=== 所有组件初始化成功 ===")
	fmt.Println("程序应该能够正常启动")
	
	// 清理测试
	os.Exit(0)
}
