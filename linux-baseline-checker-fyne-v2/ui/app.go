package ui

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	"linux-baseline-checker/config"
	"linux-baseline-checker/core"
	"linux-baseline-checker/data"
)

// App 主应用结构
type App struct {
	// Fyne应用和窗口
	fyneApp fyne.App
	window  fyne.Window

	// 核心组件
	storage *data.Storage
	config  *config.Config
	scanner *core.Scanner

	// UI组件
	tabs      *container.AppTabs
	hostTab   *HostTab
	scanTab   *ScanTab
	reportTab *ReportTab

	// 状态栏
	statusBar *fyne.Container
	timeLabel *widget.Label

	// 数据
	hosts  []data.HostInfo
	groups []data.HostGroup
}

// NewApp 创建新应用
func NewApp() *App {
	// 创建Fyne应用
	fyneApp := app.NewWithID("com.example.linux-baseline-checker")
	fyneApp.SetIcon(theme.ComputerIcon())

	// 创建存储
	storage := data.NewStorage("")

	// 创建配置管理器
	cfg := config.NewConfig(storage)

	// 创建扫描器
	scanner := core.NewScanner(
		storage,
		cfg.GetSSHTimeout(),
		cfg.GetScanTimeout(),
		cfg.GetMaxConcurrency(),
	)

	// 创建应用实例
	application := &App{
		fyneApp: fyneApp,
		storage: storage,
		config:  cfg,
		scanner: scanner,
	}

	// 创建窗口
	application.createWindow()

	// 初始化UI
	application.initializeUI()

	// 加载数据
	application.loadData()

	// 启动后台任务
	application.startBackgroundTasks()

	return application
}

// createWindow 创建主窗口
func (a *App) createWindow() {
	a.window = a.fyneApp.NewWindow("Linux远程基线核查工具 v3.2.0")

	// 设置窗口大小和位置
	width, height := a.config.GetWindowSize()
	a.window.Resize(fyne.NewSize(float32(width), float32(height)))

	x, y := a.config.GetWindowPosition()
	if x >= 0 && y >= 0 {
		// Note: Move method may not be available in all Fyne versions
		// a.window.Move(fyne.NewPos(float32(x), float32(y)))
	} else {
		a.window.CenterOnScreen()
	}

	// 设置窗口关闭回调
	a.window.SetCloseIntercept(func() {
		a.onWindowClose()
	})
}

// initializeUI 初始化用户界面
func (a *App) initializeUI() {
	// 创建状态栏
	a.createStatusBar()

	// 创建各个选项卡
	a.hostTab = NewHostTab(a.storage, a.scanner, a.updateStatus)
	a.hostTab.SetWindow(a.window) // 设置窗口引用
	a.scanTab = NewScanTab(a.storage, a.scanner, a.updateStatus)
	a.reportTab = NewReportTab(a.storage, a.updateStatus)

	// 创建选项卡容器
	a.tabs = container.NewAppTabs(
		container.NewTabItem("🖥️ 主机管理", a.hostTab.Create()),
		container.NewTabItem("🔍 执行扫描", a.scanTab.Create()),
		container.NewTabItem("📊 扫描报告", a.reportTab.Create()),
	)

	// 设置选项卡变更回调
	a.tabs.SetTabLocation(container.TabLocationTop)

	// 创建主布局
	content := container.NewBorder(
		nil,         // top
		a.statusBar, // bottom
		nil,         // left
		nil,         // right
		a.tabs,      // center
	)

	a.window.SetContent(content)
}

// createStatusBar 创建状态栏
func (a *App) createStatusBar() {
	statusLabel := widget.NewLabel("就绪")
	a.timeLabel = widget.NewLabel(time.Now().Format("15:04:05"))

	statusContainer := container.NewBorder(
		nil, nil,
		statusLabel,
		a.timeLabel,
		nil,
	)

	a.statusBar = statusContainer
}

// updateStatus 更新状态栏
func (a *App) updateStatus(message string) {
	// 简化状态更新，避免类型断言错误
	// TODO: 实现更好的状态栏更新机制
}

// loadData 加载数据
func (a *App) loadData() {
	// 加载主机数据
	if hosts, err := a.storage.LoadHosts(); err == nil {
		a.hosts = hosts
	}

	// 加载主机组数据
	if groups, err := a.storage.LoadGroups(); err == nil {
		a.groups = groups
	}

	// 刷新主机管理选项卡
	a.hostTab.RefreshData(a.hosts, a.groups)

	// 更新扫描选项卡数据
	a.scanTab.RefreshData(a.hosts, a.groups)

	// 更新报告选项卡
	a.reportTab.RefreshData()
}

// startBackgroundTasks 启动后台任务
func (a *App) startBackgroundTasks() {
	// 更新时间显示
	go func() {
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()

		for range ticker.C {
			if a.timeLabel != nil {
				a.timeLabel.SetText(time.Now().Format("15:04:05"))
			}
		}
	}()

	// 监听扫描进度
	go func() {
		progressChan := a.scanner.GetProgressChannel()
		for progress := range progressChan {
			a.updateStatus(progress.Message)
			// 通知扫描选项卡更新进度
			a.scanTab.UpdateProgress(progress)
		}
	}()

	// 定期保存配置
	if a.config.GetAutoSave() {
		go func() {
			ticker := time.NewTicker(5 * time.Minute)
			defer ticker.Stop()

			for range ticker.C {
				a.saveConfig()
			}
		}()
	}
}

// onWindowClose 窗口关闭处理
func (a *App) onWindowClose() {
	// 保存窗口大小（位置可能不可用）
	size := a.window.Content().Size()
	a.config.SetWindowSize(int(size.Width), int(size.Height))

	// 保存配置
	a.saveConfig()

	// 关闭应用
	a.fyneApp.Quit()
}

// saveConfig 保存配置
func (a *App) saveConfig() {
	if err := a.config.SaveConfig(); err != nil {
		a.updateStatus("保存配置失败: " + err.Error())
	}
}

// RefreshData 刷新数据
func (a *App) RefreshData() {
	a.loadData()
	a.updateStatus("数据已刷新")
}

// GetWindow 获取窗口
func (a *App) GetWindow() fyne.Window {
	return a.window
}

// GetStorage 获取存储
func (a *App) GetStorage() *data.Storage {
	return a.storage
}

// GetConfig 获取配置
func (a *App) GetConfig() *config.Config {
	return a.config
}

// GetScanner 获取扫描器
func (a *App) GetScanner() *core.Scanner {
	return a.scanner
}

// Run 运行应用
func (a *App) Run() {
	a.window.ShowAndRun()
}

// ShowError 显示错误对话框
func (a *App) ShowError(title, message string) {
	dialog.ShowError(fmt.Errorf("%s: %s", title, message), a.window)
}

// ShowInfo 显示信息对话框
func (a *App) ShowInfo(title, message string) {
	dialog.ShowInformation(title, message, a.window)
}

// ShowConfirm 显示确认对话框
func (a *App) ShowConfirm(title, message string, callback func(bool)) {
	dialog.ShowConfirm(title, message, callback, a.window)
}

// SwitchToTab 切换到指定选项卡
func (a *App) SwitchToTab(index int) {
	if index >= 0 && index < len(a.tabs.Items) {
		a.tabs.SelectTab(a.tabs.Items[index])
	}
}

// GetCurrentTabIndex 获取当前选项卡索引
func (a *App) GetCurrentTabIndex() int {
	for i, item := range a.tabs.Items {
		if item == a.tabs.Selected() {
			return i
		}
	}
	return 0
}
