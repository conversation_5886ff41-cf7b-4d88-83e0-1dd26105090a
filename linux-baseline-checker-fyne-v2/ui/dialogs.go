package ui

import (
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/widget"

	"linux-baseline-checker/core"
	"linux-baseline-checker/data"
	"linux-baseline-checker/utils"
)

// AddHostDialog 添加主机对话框
type AddHostDialog struct {
	window       fyne.Window
	storage      *data.Storage
	updateStatus func(string)
	callback     func()

	// 表单组件
	nameEntry     *widget.Entry
	hostEntry     *widget.Entry
	portEntry     *widget.Entry
	usernameEntry *widget.Entry
	passwordEntry *widget.Entry
	descEntry     *widget.Entry

	// 编辑模式
	editMode bool
	editHost *data.HostInfo
}

// NewAddHostDialog 创建添加主机对话框
func NewAddHostDialog(window fyne.Window, storage *data.Storage, updateStatus func(string), callback func()) *AddHostDialog {
	return &AddHostDialog{
		window:       window,
		storage:      storage,
		updateStatus: updateStatus,
		callback:     callback,
	}
}

// Show 显示对话框
func (d *AddHostDialog) Show() {
	d.createForm()

	title := "添加主机"
	if d.editMode {
		title = "编辑主机"
	}

	// 使用Border布局重构主机对话框
	// 1. 主要表单区域
	formSection := container.NewPadded(
		widget.NewCard("", "主机信息", container.NewVBox(
			container.NewGridWithColumns(2,
				widget.NewLabel("主机名称:"), d.nameEntry,
				widget.NewLabel("主机地址:"), d.hostEntry,
				widget.NewLabel("端口:"), d.portEntry,
				widget.NewLabel("用户名:"), d.usernameEntry,
				widget.NewLabel("密码:"), d.passwordEntry,
			),
			widget.NewLabel("描述:"),
			d.descEntry,
		)),
	)

	// 2. 底部按钮区域
	buttonSection := container.NewPadded(
		container.NewHBox(
			widget.NewButton("测试连接", d.testConnection),
			widget.NewButton("确定", func() {
				// 确定按钮功能将在后面设置
			}),
			widget.NewButton("取消", func() {
				// 取消按钮关闭对话框
			}),
		),
	)

	// 3. 整体布局：使用Border布局
	content := container.NewBorder(
		nil,           // 顶部
		buttonSection, // 底部：按钮
		nil,           // 左侧
		nil,           // 右侧
		formSection,   // 中心：表单
	)

	// 创建弹出窗口而不是对话框
	popup := widget.NewModalPopUp(
		container.NewVBox(
			widget.NewLabel(title),
			widget.NewSeparator(),
			content,
		),
		d.window.Canvas(),
	)
	popup.Resize(fyne.NewSize(350, 300))

	// 设置按钮的关闭功能
	buttonContainer := buttonSection.Objects[0].(*fyne.Container)
	confirmBtn := buttonContainer.Objects[1].(*widget.Button)
	cancelBtn := buttonContainer.Objects[2].(*widget.Button)

	// 设置确定按钮功能，只有保存成功时才关闭
	confirmBtn.OnTapped = func() {
		if d.save() { // 只有保存成功时才关闭对话框
			popup.Hide()
		}
	}

	cancelBtn.OnTapped = func() {
		popup.Hide()
	}

	popup.Show()
}

// ShowEdit 显示编辑对话框
func (d *AddHostDialog) ShowEdit(host data.HostInfo) {
	d.editMode = true
	d.editHost = &host
	d.Show()

	// 填充表单
	d.nameEntry.SetText(host.Name)
	d.hostEntry.SetText(host.Host)
	d.portEntry.SetText(host.Port)
	d.usernameEntry.SetText(host.Username)
	d.passwordEntry.SetText(host.Password)
	d.descEntry.SetText(host.Description)
}

// createForm 创建表单组件
func (d *AddHostDialog) createForm() {
	d.nameEntry = widget.NewEntry()
	d.nameEntry.SetPlaceHolder("给主机起个名字")

	d.hostEntry = widget.NewEntry()
	d.hostEntry.SetPlaceHolder("*************")

	d.portEntry = widget.NewEntry()
	d.portEntry.SetPlaceHolder("22")
	d.portEntry.SetText("22")

	d.usernameEntry = widget.NewEntry()
	d.usernameEntry.SetPlaceHolder("root")

	d.passwordEntry = widget.NewPasswordEntry()
	d.passwordEntry.SetPlaceHolder("请输入密码")

	d.descEntry = widget.NewMultiLineEntry()
	d.descEntry.SetPlaceHolder("主机描述（可选）")
	d.descEntry.Resize(fyne.NewSize(350, 60))
}

// testConnection 测试连接
func (d *AddHostDialog) testConnection() {
	host := d.getHostFromForm()
	if errors := utils.ValidateHostInfo(host); len(errors) > 0 {
		d.updateStatus("验证失败: " + strings.Join(errors, "; "))
		dialog.ShowError(fmt.Errorf("验证失败: %s", strings.Join(errors, "; ")), d.window)
		return
	}

	d.updateStatus("正在测试连接...")

	// 创建进度对话框
	progressBar := widget.NewProgressBarInfinite()
	progressBar.Start()

	statusLabel := widget.NewLabel("正在连接到主机...")

	content := container.NewVBox(
		widget.NewLabel("连接测试"),
		widget.NewSeparator(),
		statusLabel,
		progressBar,
	)

	testDialog := widget.NewModalPopUp(content, d.window.Canvas())
	testDialog.Resize(fyne.NewSize(300, 150))
	testDialog.Show()

	// 在后台执行连接测试
	go func() {
		// 创建SSH客户端进行测试
		sshClient := core.NewSSHClient(host, 30*time.Second)

		err := sshClient.TestConnection(host)

		// 在UI线程中更新结果
		progressBar.Stop()
		testDialog.Hide()

		if err != nil {
			d.updateStatus("连接测试失败: " + err.Error())
			dialog.ShowError(fmt.Errorf("连接测试失败: %v", err), d.window)
		} else {
			d.updateStatus("连接测试成功")
			dialog.ShowInformation("连接测试", "连接测试成功！", d.window)
		}
	}()
}

// save 保存主机
func (d *AddHostDialog) save() bool {
	host := d.getHostFromForm()
	if errors := utils.ValidateHostInfo(host); len(errors) > 0 {
		dialog.ShowError(fmt.Errorf("验证失败: %s", strings.Join(errors, "; ")), d.window)
		return false
	}

	// 设置ID
	if d.editMode && d.editHost != nil {
		host.ID = d.editHost.ID
	} else {
		host.ID = utils.GenerateID("host")
	}

	if err := d.storage.SaveHost(host); err != nil {
		d.updateStatus("保存失败: " + err.Error())
		dialog.ShowError(fmt.Errorf("保存失败: %v", err), d.window)
		return false
	}

	action := "添加"
	if d.editMode {
		action = "更新"
	}

	d.updateStatus(fmt.Sprintf("主机 \"%s\" %s成功", host.Name, action))
	dialog.ShowInformation("保存成功", fmt.Sprintf("主机 \"%s\" %s成功", host.Name, action), d.window)

	if d.callback != nil {
		d.callback()
	}

	return true
}

// getHostFromForm 从表单获取主机信息
func (d *AddHostDialog) getHostFromForm() data.HostInfo {
	host := data.HostInfo{
		Name:        strings.TrimSpace(d.nameEntry.Text),
		Host:        strings.TrimSpace(d.hostEntry.Text),
		Port:        strings.TrimSpace(d.portEntry.Text),
		Username:    strings.TrimSpace(d.usernameEntry.Text),
		Password:    d.passwordEntry.Text,
		Description: strings.TrimSpace(d.descEntry.Text),
		Status:      "未扫描",
	}

	if host.Name == "" {
		host.Name = host.Host
	}

	return host
}

// AddGroupDialog 添加主机组对话框
type AddGroupDialog struct {
	window       fyne.Window
	storage      *data.Storage
	updateStatus func(string)
	callback     func()

	// 表单组件
	nameEntry     *widget.Entry
	descEntry     *widget.Entry
	hostList      *widget.List
	addHostBtn    *widget.Button
	removeHostBtn *widget.Button
	testConnBtn   *widget.Button

	// 数据
	groupHosts []data.HostInfo // 主机组中的主机列表

	// 编辑模式
	editMode  bool
	editGroup *data.HostGroup
}

// NewAddGroupDialog 创建添加主机组对话框
func NewAddGroupDialog(window fyne.Window, storage *data.Storage, updateStatus func(string), callback func()) *AddGroupDialog {
	return &AddGroupDialog{
		window:       window,
		storage:      storage,
		updateStatus: updateStatus,
		callback:     callback,
		groupHosts:   make([]data.HostInfo, 0),
	}
}

// Show 显示对话框
func (d *AddGroupDialog) Show() {
	d.createForm()

	title := "添加主机组"
	if d.editMode {
		title = "编辑主机组"
	}

	// 严格按照图片布局设计 - 使用Border布局让主机列表区域占据大部分空间

	// 1. 顶部：主机组信息区域（优化布局）
	topSection := container.NewVBox(
		widget.NewCard("", "主机组信息", container.NewVBox(
			container.NewGridWithColumns(2,
				widget.NewLabel("组名称:"),
				d.nameEntry,
			),
			container.NewGridWithColumns(2,
				widget.NewLabel("描述:"),
				d.descEntry,
			),
		)),
	)

	// 2. 底部：确定取消按钮（固定高度）
	bottomSection := container.NewHBox(
		widget.NewButton("确定", func() {
			// 确定按钮功能将在后面设置
		}),
		layout.NewSpacer(),
		widget.NewButton("取消", func() {
			// 取消按钮关闭对话框
		}),
	)

	// 3. 中间：主机列表区域（占据剩余空间）
	// 创建紧凑的主机列表容器，支持滚动
	listScroll := container.NewScroll(d.hostList)
	listScroll.SetMinSize(fyne.NewSize(300, 120)) // 适合350x350对话框的尺寸

	middleSection := widget.NewCard("", "主机列表", container.NewBorder(
		nil, // 顶部留空
		container.NewHBox(
			d.addHostBtn,
			d.removeHostBtn,
			d.testConnBtn,
		),
		nil, nil,
		// 主机列表占据中心空间 - 使用滚动容器
		listScroll,
	))

	// 4. 整体布局：使用Border布局
	content := container.NewBorder(
		topSection,    // 顶部：主机组信息
		bottomSection, // 底部：确定取消按钮
		nil, nil,      // 左右为空
		middleSection, // 中心：主机列表区域（自动扩展）
	)

	// 创建弹出窗口而不是对话框
	popup := widget.NewModalPopUp(
		container.NewVBox(
			widget.NewLabel(title),
			widget.NewSeparator(),
			content,
		),
		d.window.Canvas(),
	)
	popup.Resize(fyne.NewSize(350, 350))

	// 设置按钮的关闭功能
	confirmBtn := bottomSection.Objects[0].(*widget.Button)
	cancelBtn := bottomSection.Objects[2].(*widget.Button) // 跳过spacer，取第3个对象

	// 设置确定按钮功能，只有保存成功时才关闭
	confirmBtn.OnTapped = func() {
		if d.save() { // 只有保存成功时才关闭对话框
			popup.Hide()
		}
	}

	cancelBtn.OnTapped = func() {
		popup.Hide()
	}

	popup.Show()
}

// ShowEdit 显示编辑对话框
func (d *AddGroupDialog) ShowEdit(group data.HostGroup) {
	d.editMode = true
	d.editGroup = &group

	// 复制主机组中的主机到本地列表
	d.groupHosts = make([]data.HostInfo, len(group.Hosts))
	copy(d.groupHosts, group.Hosts)

	d.Show()

	// 填充表单
	d.nameEntry.SetText(group.Name)
	d.descEntry.SetText(group.Description)
}

// createForm 创建表单组件
func (d *AddGroupDialog) createForm() {
	d.nameEntry = widget.NewEntry()
	d.nameEntry.SetPlaceHolder("输入主机组名称")
	d.nameEntry.Resize(fyne.NewSize(300, 40)) // 设置更大的输入框

	d.descEntry = widget.NewMultiLineEntry()
	d.descEntry.SetPlaceHolder("输入主机组描述")
	d.descEntry.Resize(fyne.NewSize(300, 60)) // 调整描述框尺寸

	// 创建按钮
	d.addHostBtn = widget.NewButton("+ 添加主机", d.showAddHostToGroupDialog)
	d.removeHostBtn = widget.NewButton("- 移除主机", d.removeSelectedHost)
	d.testConnBtn = widget.NewButton("测试连接", d.testAllConnections)
	d.removeHostBtn.Disable() // 初始状态禁用
	d.testConnBtn.Disable()   // 初始状态禁用

	// 创建主机列表 - 关键：创建一个自定义的List widget
	d.hostList = &widget.List{
		Length: func() int {
			return len(d.groupHosts)
		},
		CreateItem: func() fyne.CanvasObject {
			nameLabel := widget.NewLabel("主机名")
			ipLabel := widget.NewLabel("IP地址")
			userLabel := widget.NewLabel("用户")

			// 设置标签样式和固定宽度
			nameLabel.TextStyle = fyne.TextStyle{Bold: true}
			nameLabel.Resize(fyne.NewSize(80, 30))

			ipLabel.TextStyle = fyne.TextStyle{}
			ipLabel.Resize(fyne.NewSize(120, 30))

			userLabel.TextStyle = fyne.TextStyle{Italic: true}
			userLabel.Resize(fyne.NewSize(80, 30))

			// 使用水平布局，一行显示所有信息
			content := container.NewHBox(
				nameLabel,
				ipLabel,
				userLabel,
			)

			return content
		},
		UpdateItem: func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(d.groupHosts) {
				host := d.groupHosts[id]
				containerObj := obj.(*fyne.Container)
				nameLabel := containerObj.Objects[0].(*widget.Label)
				ipLabel := containerObj.Objects[1].(*widget.Label)
				userLabel := containerObj.Objects[2].(*widget.Label)

				// 紧凑的一行显示格式
				nameLabel.SetText(fmt.Sprintf("%d.%s", id+1, host.Name))
				ipLabel.SetText(fmt.Sprintf("%s:%s", host.Host, host.Port))
				userLabel.SetText(host.Username)
			}
		},
	}

	// 关键：正确初始化List widget并设置最小尺寸
	d.hostList.ExtendBaseWidget(d.hostList)

	// 设置列表选择回调
	d.hostList.OnSelected = func(id widget.ListItemID) {
		d.removeHostBtn.Enable()
	}

	// 当有主机时启用测试连接按钮
	if len(d.groupHosts) > 0 {
		d.testConnBtn.Enable()
	}
}

// showAddHostToGroupDialog 显示添加主机到组的对话框
func (d *AddGroupDialog) showAddHostToGroupDialog() {
	// 创建一个简化的主机添加对话框
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("主机名称")

	hostEntry := widget.NewEntry()
	hostEntry.SetPlaceHolder("IP地址")

	portEntry := widget.NewEntry()
	portEntry.SetPlaceHolder("端口")
	portEntry.SetText("22")

	usernameEntry := widget.NewEntry()
	usernameEntry.SetPlaceHolder("用户名")

	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.SetPlaceHolder("密码")

	descEntry := widget.NewEntry()
	descEntry.SetPlaceHolder("描述（可选）")

	content := container.NewVBox(
		widget.NewCard("添加主机到组", "", container.NewVBox(
			container.NewGridWithColumns(2,
				widget.NewLabel("主机名称:"), nameEntry,
				widget.NewLabel("IP地址:"), hostEntry,
				widget.NewLabel("端口:"), portEntry,
				widget.NewLabel("用户名:"), usernameEntry,
				widget.NewLabel("密码:"), passwordEntry,
				widget.NewLabel("描述:"), descEntry,
			),
		)),
		container.NewHBox(
			widget.NewButton("添加", func() {
				// 验证输入
				if nameEntry.Text == "" || hostEntry.Text == "" {
					dialog.ShowError(fmt.Errorf("主机名称和IP地址不能为空"), d.window)
					return
				}

				// 创建主机信息
				host := data.HostInfo{
					ID:          utils.GenerateID("host"),
					Name:        nameEntry.Text,
					Host:        hostEntry.Text,
					Port:        portEntry.Text,
					Username:    usernameEntry.Text,
					Password:    passwordEntry.Text,
					Description: descEntry.Text,
					Status:      "未扫描",
				}

				// 添加到组列表
				d.groupHosts = append(d.groupHosts, host)
				d.hostList.Refresh()

				// 关闭对话框
				// popup.Hide() // 这里需要引用popup
			}),
			widget.NewButton("取消", func() {
				// popup.Hide() // 这里需要引用popup
			}),
		),
	)

	popup := widget.NewModalPopUp(
		container.NewVBox(
			widget.NewLabel("添加主机"),
			widget.NewSeparator(),
			content,
		),
		d.window.Canvas(),
	)
	popup.Resize(fyne.NewSize(350, 400))

	// 设置按钮的关闭功能
	buttons := content.Objects[1].(*fyne.Container)
	addBtn := buttons.Objects[0].(*widget.Button)
	cancelBtn := buttons.Objects[1].(*widget.Button)

	addBtn.OnTapped = func() {
		// 验证输入
		if nameEntry.Text == "" || hostEntry.Text == "" {
			dialog.ShowError(fmt.Errorf("主机名称和IP地址不能为空"), d.window)
			return
		}

		// 创建主机信息
		host := data.HostInfo{
			ID:          fmt.Sprintf("host_%d", len(d.groupHosts)+1),
			Name:        nameEntry.Text,
			Host:        hostEntry.Text,
			Port:        portEntry.Text,
			Username:    usernameEntry.Text,
			Password:    passwordEntry.Text,
			Description: descEntry.Text,
			Status:      "未扫描",
		}

		// 添加到组列表
		d.groupHosts = append(d.groupHosts, host)
		d.hostList.Refresh()

		// 启用测试连接按钮
		if len(d.groupHosts) > 0 {
			d.testConnBtn.Enable()
		}

		popup.Hide()
	}

	cancelBtn.OnTapped = func() {
		popup.Hide()
	}

	popup.Show()
}

// removeSelectedHost 移除选中的主机
func (d *AddGroupDialog) removeSelectedHost() {
	// 这里需要获取选中的主机索引
	// 由于Fyne的List组件限制，我们简化为移除最后一个
	if len(d.groupHosts) > 0 {
		d.groupHosts = d.groupHosts[:len(d.groupHosts)-1]
		d.hostList.Refresh()
		if len(d.groupHosts) == 0 {
			d.removeHostBtn.Disable()
			d.testConnBtn.Disable()
		}
	}
}

// testAllConnections 测试所有主机连接
func (d *AddGroupDialog) testAllConnections() {
	if len(d.groupHosts) == 0 {
		dialog.ShowInformation("提示", "没有主机需要测试", d.window)
		return
	}

	d.updateStatus("正在测试所有主机连接...")

	// 创建结果显示对话框
	resultText := widget.NewRichTextFromMarkdown("正在测试连接，请稍候...")
	resultScroll := container.NewScroll(resultText)

	closeBtn := widget.NewButton("关闭", func() {
		// 关闭按钮功能将在后面设置
	})

	resultContent := container.NewBorder(
		container.NewVBox(
			widget.NewLabel("连接测试结果"),
			widget.NewSeparator(),
		), // 顶部
		closeBtn,     // 底部
		nil,          // 左侧
		nil,          // 右侧
		resultScroll, // 中心
	)

	resultDialog := widget.NewModalPopUp(
		resultContent,
		d.window.Canvas(),
	)
	resultDialog.Resize(fyne.NewSize(500, 450))

	// 设置关闭按钮功能
	closeBtn.OnTapped = func() {
		resultDialog.Hide()
	}

	resultDialog.Show()

	// 开始测试连接
	go func() {
		var results []string
		successCount := 0
		failCount := 0

		for i, host := range d.groupHosts {
			result := fmt.Sprintf("%d. **%s** (%s:%s)", i+1, host.Name, host.Host, host.Port)

			// 严格验证主机信息
			var errors []string
			if host.Host == "" {
				errors = append(errors, "主机地址为空")
			}
			if host.Username == "" {
				errors = append(errors, "用户名为空")
			}
			if host.Password == "" {
				errors = append(errors, "密码为空")
			}
			if host.Port == "" {
				errors = append(errors, "端口为空")
			}

			if len(errors) > 0 {
				result += " - ❌ **连接失败**: " + strings.Join(errors, ", ")
				failCount++
			} else {
				// 实际测试SSH连接
				sshClient := core.NewSSHClient(host, 30*time.Second)
				if err := sshClient.TestConnection(host); err != nil {
					result += " - ❌ **连接失败**: " + err.Error()
					failCount++
				} else {
					result += " - ✅ **连接成功**"
					successCount++
				}
			}

			results = append(results, result)

			// 更新结果显示
			resultMarkdown := fmt.Sprintf("## 连接测试结果\n\n**总计**: %d台主机 | **成功**: %d台 | **失败**: %d台\n\n---\n\n%s",
				len(d.groupHosts), successCount, failCount, strings.Join(results, "\n\n"))

			resultText.ParseMarkdown(resultMarkdown)
		}

		// 更新状态
		if failCount > 0 {
			d.updateStatus(fmt.Sprintf("连接测试完成，%d台成功，%d台失败", successCount, failCount))
		} else {
			d.updateStatus(fmt.Sprintf("连接测试完成，全部%d台主机连接成功", successCount))
		}
	}()
}

// save 保存主机组
func (d *AddGroupDialog) save() bool {
	name := strings.TrimSpace(d.nameEntry.Text)
	desc := strings.TrimSpace(d.descEntry.Text)

	if name == "" {
		dialog.ShowError(fmt.Errorf("主机组名称不能为空"), d.window)
		return false
	}

	group := data.HostGroup{
		Name:        name,
		Description: desc,
		Hosts:       d.groupHosts, // 使用组内的主机列表
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 设置ID
	if d.editMode && d.editGroup != nil {
		group.ID = d.editGroup.ID
		group.CreatedAt = d.editGroup.CreatedAt
	} else {
		group.ID = utils.GenerateID("group")
	}

	if err := d.storage.SaveGroup(group); err != nil {
		d.updateStatus("保存失败: " + err.Error())
		dialog.ShowError(fmt.Errorf("保存失败: %v", err), d.window)
		return false
	}

	action := "创建"
	if d.editMode {
		action = "更新"
	}

	d.updateStatus(fmt.Sprintf("主机组 \"%s\" %s成功", name, action))
	dialog.ShowInformation("保存成功", fmt.Sprintf("主机组 \"%s\" %s成功", name, action), d.window)

	if d.callback != nil {
		d.callback()
	}

	return true
}
