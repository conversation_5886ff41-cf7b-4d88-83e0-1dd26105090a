package ui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"linux-baseline-checker/data"
)

// ReportTab 报告选项卡
type ReportTab struct {
	storage      *data.Storage
	updateStatus func(string)
	
	// UI组件
	container    *fyne.Container
	statsCard    *widget.Card
}

// NewReportTab 创建报告选项卡
func NewReportTab(storage *data.Storage, updateStatus func(string)) *ReportTab {
	return &ReportTab{
		storage:      storage,
		updateStatus: updateStatus,
	}
}

// Create 创建选项卡内容
func (r *ReportTab) Create() fyne.CanvasObject {
	// 统计信息卡片
	r.statsCard = widget.NewCard("统计信息", "", container.NewVBox(
		widget.NewLabel("总主机数: 0"),
		widget.NewLabel("主机组数: 0"),
		widget.NewLabel("扫描次数: 0"),
		widget.NewLabel("功能开发中..."),
	))
	
	r.container = container.NewVBox(
		r.statsCard,
	)
	
	return r.container
}

// RefreshData 刷新数据
func (r *ReportTab) RefreshData() {
	// TODO: 更新统计信息
}
