package ui

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"linux-baseline-checker/core"
	"linux-baseline-checker/data"
	"linux-baseline-checker/utils"
)

// ScanTab 扫描选项卡
type ScanTab struct {
	storage      *data.Storage
	scanner      *core.Scanner
	updateStatus func(string)
	window       fyne.Window

	// UI组件
	container      *container.Split
	scanTypeSelect *widget.Select
	hostSelector   *widget.Select
	groupSelector  *widget.Select
	startBtn       *widget.Button
	stopBtn        *widget.Button
	progressBar    *widget.ProgressBar
	statusLabel    *widget.Label
	resultsList    *widget.List

	// 数据
	hosts       []data.HostInfo
	groups      []data.HostGroup
	results     []data.ScanResult
	isScanning  bool
	currentScan string
}

// NewScanTab 创建扫描选项卡
func NewScanTab(storage *data.Storage, scanner *core.Scanner, updateStatus func(string)) *ScanTab {
	return &ScanTab{
		storage:      storage,
		scanner:      scanner,
		updateStatus: updateStatus,
		hosts:        make([]data.HostInfo, 0),
		groups:       make([]data.HostGroup, 0),
		results:      make([]data.ScanResult, 0),
		isScanning:   false,
	}
}

// Create 创建选项卡内容
func (s *ScanTab) Create() fyne.CanvasObject {
	s.createComponents()

	// 左侧：扫描配置
	leftPanel := container.NewVBox(
		widget.NewCard("", "扫描配置", container.NewVBox(
			widget.NewLabel("扫描类型:"),
			s.scanTypeSelect,
			widget.NewSeparator(),
			widget.NewLabel("选择主机:"),
			s.hostSelector,
			widget.NewLabel("选择主机组:"),
			s.groupSelector,
			widget.NewSeparator(),
			container.NewHBox(s.startBtn, s.stopBtn),
		)),

		widget.NewCard("", "扫描状态", container.NewVBox(
			s.statusLabel,
			s.progressBar,
		)),
	)

	// 右侧：扫描结果
	rightPanel := container.NewVBox(
		widget.NewCard("", "扫描结果", container.NewBorder(
			nil, // 顶部
			container.NewHBox(
				widget.NewButton("导出结果", s.exportResults),
				widget.NewButton("清空结果", s.clearResults),
			), // 底部
			nil, nil, // 左右
			container.NewScroll(s.resultsList), // 中心
		)),
	)

	s.container = container.NewHSplit(leftPanel, rightPanel)
	s.container.SetOffset(0.35) // 左侧35%，右侧65%

	return s.container
}

// createComponents 创建UI组件
func (s *ScanTab) createComponents() {
	// 扫描类型选择
	s.scanTypeSelect = widget.NewSelect(
		[]string{"单主机扫描", "主机组扫描"},
		func(value string) {
			if value == "单主机扫描" {
				s.hostSelector.Enable()
				s.groupSelector.Disable()
				s.groupSelector.ClearSelected()
			} else {
				s.hostSelector.Disable()
				s.hostSelector.ClearSelected()
				s.groupSelector.Enable()
			}
		},
	)
	s.scanTypeSelect.SetSelected("单主机扫描")

	// 主机选择器
	s.hostSelector = widget.NewSelect([]string{}, func(value string) {})

	// 主机组选择器
	s.groupSelector = widget.NewSelect([]string{}, func(value string) {})
	s.groupSelector.Disable()

	// 按钮
	s.startBtn = widget.NewButton("开始扫描", s.startScan)
	s.stopBtn = widget.NewButton("停止扫描", s.stopScan)
	s.stopBtn.Disable()

	// 进度条和状态
	s.progressBar = widget.NewProgressBar()
	s.statusLabel = widget.NewLabel("就绪")

	// 结果列表
	s.resultsList = widget.NewList(
		func() int {
			return len(s.results)
		},
		func() fyne.CanvasObject {
			seqLabel := widget.NewLabel("1.")
			hostLabel := widget.NewLabel("主机名")
			statusLabel := widget.NewLabel("状态")
			scoreLabel := widget.NewLabel("得分")
			timeLabel := widget.NewLabel("时间")

			seqLabel.TextStyle = fyne.TextStyle{Bold: true}
			hostLabel.TextStyle = fyne.TextStyle{Bold: true}

			return container.NewHBox(
				container.NewPadded(seqLabel),
				container.NewVBox(hostLabel, statusLabel),
				container.NewVBox(scoreLabel, timeLabel),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(s.results) {
				result := s.results[id]
				containerObj := obj.(*fyne.Container)
				seqContainer := containerObj.Objects[0].(*fyne.Container)
				infoContainer := containerObj.Objects[1].(*fyne.Container)
				scoreContainer := containerObj.Objects[2].(*fyne.Container)

				seqLabel := seqContainer.Objects[0].(*widget.Label)
				hostLabel := infoContainer.Objects[0].(*widget.Label)
				statusLabel := infoContainer.Objects[1].(*widget.Label)
				scoreLabel := scoreContainer.Objects[0].(*widget.Label)
				timeLabel := scoreContainer.Objects[1].(*widget.Label)

				seqLabel.SetText(fmt.Sprintf("%d.", id+1))
				hostLabel.SetText(result.HostName)
				statusLabel.SetText(result.Status)

				if result.MaxScore > 0 {
					percentage := utils.CalculateScorePercentage(result.TotalScore, result.MaxScore)
					scoreLabel.SetText(fmt.Sprintf("%.1f%%", percentage))
				} else {
					scoreLabel.SetText("N/A")
				}

				timeLabel.SetText(utils.FormatTime(result.StartTime))
			}
		},
	)
}

// startScan 开始扫描
func (s *ScanTab) startScan() {
	if s.isScanning {
		return
	}

	scanType := s.scanTypeSelect.Selected
	if scanType == "" {
		dialog.ShowError(fmt.Errorf("请选择扫描类型"), s.window)
		return
	}

	if scanType == "单主机扫描" {
		s.startSingleHostScan()
	} else {
		s.startGroupScan()
	}
}

// startSingleHostScan 开始单主机扫描
func (s *ScanTab) startSingleHostScan() {
	selectedHost := s.hostSelector.Selected
	if selectedHost == "" {
		dialog.ShowError(fmt.Errorf("请选择要扫描的主机"), s.window)
		return
	}

	// 找到选中的主机
	var host *data.HostInfo
	for _, h := range s.hosts {
		if h.Name == selectedHost {
			host = &h
			break
		}
	}

	if host == nil {
		dialog.ShowError(fmt.Errorf("未找到选中的主机"), s.window)
		return
	}

	s.isScanning = true
	s.startBtn.Disable()
	s.stopBtn.Enable()
	s.progressBar.SetValue(0)
	s.statusLabel.SetText(fmt.Sprintf("正在扫描主机: %s", host.Name))
	s.updateStatus(fmt.Sprintf("开始扫描主机: %s", host.Name))

	// 在后台执行扫描
	go func() {
		result, err := s.scanner.ScanSingleHost(*host)

		// 更新UI（在主线程中）
		s.isScanning = false
		s.startBtn.Enable()
		s.stopBtn.Disable()
		s.progressBar.SetValue(1.0)

		if err != nil {
			s.statusLabel.SetText(fmt.Sprintf("扫描失败: %v", err))
			s.updateStatus(fmt.Sprintf("扫描失败: %v", err))
			dialog.ShowError(fmt.Errorf("扫描失败: %v", err), s.window)
		} else {
			s.results = append(s.results, *result)
			s.resultsList.Refresh()
			s.statusLabel.SetText("扫描完成")
			s.updateStatus(fmt.Sprintf("主机 %s 扫描完成", host.Name))
		}
	}()
}

// startGroupScan 开始主机组扫描
func (s *ScanTab) startGroupScan() {
	selectedGroup := s.groupSelector.Selected
	if selectedGroup == "" {
		dialog.ShowError(fmt.Errorf("请选择要扫描的主机组"), s.window)
		return
	}

	// 找到选中的主机组
	var group *data.HostGroup
	for _, g := range s.groups {
		if g.Name == selectedGroup {
			group = &g
			break
		}
	}

	if group == nil {
		dialog.ShowError(fmt.Errorf("未找到选中的主机组"), s.window)
		return
	}

	if len(group.Hosts) == 0 {
		dialog.ShowError(fmt.Errorf("主机组中没有主机"), s.window)
		return
	}

	s.isScanning = true
	s.startBtn.Disable()
	s.stopBtn.Enable()
	s.progressBar.SetValue(0)
	s.statusLabel.SetText(fmt.Sprintf("正在扫描主机组: %s (%d台主机)", group.Name, len(group.Hosts)))
	s.updateStatus(fmt.Sprintf("开始扫描主机组: %s", group.Name))

	// 在后台执行批量扫描
	go func() {
		// 逐个扫描主机组中的主机
		var results []data.ScanResult
		var lastErr error

		for i, host := range group.Hosts {
			// 更新进度
			progress := float64(i) / float64(len(group.Hosts))
			s.progressBar.SetValue(progress)
			s.statusLabel.SetText(fmt.Sprintf("正在扫描: %s (%d/%d)", host.Name, i+1, len(group.Hosts)))

			result, err := s.scanner.ScanSingleHost(host)
			if err != nil {
				lastErr = err
				// 创建失败结果
				failedResult := &data.ScanResult{
					HostName:   host.Name,
					Status:     "扫描失败",
					StartTime:  time.Now(),
					TotalScore: 0,
					MaxScore:   0,
				}
				results = append(results, *failedResult)
			} else {
				results = append(results, *result)
			}
		}

		// 更新UI（在主线程中）
		s.isScanning = false
		s.startBtn.Enable()
		s.stopBtn.Disable()
		s.progressBar.SetValue(1.0)

		if lastErr != nil && len(results) == 0 {
			s.statusLabel.SetText(fmt.Sprintf("批量扫描失败: %v", lastErr))
			s.updateStatus(fmt.Sprintf("批量扫描失败: %v", lastErr))
			dialog.ShowError(fmt.Errorf("批量扫描失败: %v", lastErr), s.window)
		} else {
			// 添加所有扫描结果
			s.results = append(s.results, results...)
			s.resultsList.Refresh()

			successCount := 0
			for _, result := range results {
				if result.Status != "扫描失败" {
					successCount++
				}
			}

			s.statusLabel.SetText(fmt.Sprintf("批量扫描完成 (%d/%d成功)", successCount, len(results)))
			s.updateStatus(fmt.Sprintf("主机组 %s 扫描完成", group.Name))
		}
	}()
}

// stopScan 停止扫描
func (s *ScanTab) stopScan() {
	// TODO: 实现停止扫描功能
	s.updateStatus("停止扫描功能待实现")
}

// exportResults 导出结果
func (s *ScanTab) exportResults() {
	if len(s.results) == 0 {
		dialog.ShowInformation("提示", "没有扫描结果可导出", s.window)
		return
	}
	s.updateStatus("导出功能待实现")
}

// clearResults 清空结果
func (s *ScanTab) clearResults() {
	if len(s.results) == 0 {
		return
	}

	dialog.ShowConfirm("确认清空", "确定要清空所有扫描结果吗？", func(confirmed bool) {
		if confirmed {
			s.results = make([]data.ScanResult, 0)
			s.resultsList.Refresh()
			s.statusLabel.SetText("就绪")
			s.updateStatus("扫描结果已清空")
		}
	}, s.window)
}

// SetWindow 设置窗口引用
func (s *ScanTab) SetWindow(window fyne.Window) {
	s.window = window
}

// UpdateProgress 更新进度
func (s *ScanTab) UpdateProgress(progress data.ScanProgress) {
	if s.progressBar != nil {
		// Progress字段是百分比(0-100)，需要转换为0-1
		s.progressBar.SetValue(float64(progress.Progress) / 100.0)
	}
	if s.statusLabel != nil {
		if progress.Message != "" {
			s.statusLabel.SetText(progress.Message)
		} else {
			s.statusLabel.SetText(fmt.Sprintf("正在扫描: %s - %s", progress.HostName, progress.CurrentCheck))
		}
	}
}

// RefreshData 刷新数据
func (s *ScanTab) RefreshData(hosts []data.HostInfo, groups []data.HostGroup) {
	s.hosts = hosts
	s.groups = groups

	// 更新主机选择器
	if s.hostSelector != nil {
		options := make([]string, len(hosts))
		for i, host := range hosts {
			options[i] = host.Name
		}
		s.hostSelector.Options = options
		s.hostSelector.Refresh()
	}

	// 更新主机组选择器
	if s.groupSelector != nil {
		groupOptions := make([]string, len(groups))
		for i, group := range groups {
			groupOptions[i] = group.Name
		}
		s.groupSelector.Options = groupOptions
		s.groupSelector.Refresh()
	}
}
