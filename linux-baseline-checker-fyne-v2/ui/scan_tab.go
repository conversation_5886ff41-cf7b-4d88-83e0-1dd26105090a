package ui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"linux-baseline-checker/core"
	"linux-baseline-checker/data"
)

// ScanTab 扫描选项卡
type ScanTab struct {
	storage      *data.Storage
	scanner      *core.Scanner
	updateStatus func(string)
	
	// UI组件
	container    *container.Split
	hostSelector *widget.Select
	resultsList  *widget.List
	
	// 数据
	hosts   []data.HostInfo
	groups  []data.HostGroup
	results []data.ScanResult
}

// NewScanTab 创建扫描选项卡
func NewScanTab(storage *data.Storage, scanner *core.Scanner, updateStatus func(string)) *ScanTab {
	return &ScanTab{
		storage:      storage,
		scanner:      scanner,
		updateStatus: updateStatus,
		hosts:        make([]data.HostInfo, 0),
		groups:       make([]data.HostGroup, 0),
		results:      make([]data.ScanResult, 0),
	}
}

// Create 创建选项卡内容
func (s *ScanTab) Create() fyne.CanvasObject {
	// 创建主机选择器
	s.hostSelector = widget.NewSelect([]string{}, func(value string) {})
	
	// 创建结果列表
	s.resultsList = widget.NewList(
		func() int {
			return len(s.results)
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("扫描结果")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(s.results) {
				result := s.results[id]
				label := obj.(*widget.Label)
				label.SetText(result.HostName + " - " + result.Status)
			}
		},
	)
	
	// 左侧：扫描选项
	leftPanel := container.NewVBox(
		widget.NewCard("扫描选项", "", container.NewVBox(
			widget.NewLabel("选择主机:"),
			s.hostSelector,
			widget.NewButton("开始扫描", s.startScan),
			widget.NewLabel("功能开发中..."),
		)),
	)
	
	// 右侧：扫描结果
	rightPanel := container.NewVBox(
		widget.NewCard("扫描结果", "", container.NewVBox(
			s.resultsList,
		)),
	)
	
	s.container = container.NewHSplit(leftPanel, rightPanel)
	s.container.SetOffset(0.4)
	
	return s.container
}

// startScan 开始扫描
func (s *ScanTab) startScan() {
	s.updateStatus("扫描功能开发中...")
}

// UpdateProgress 更新进度
func (s *ScanTab) UpdateProgress(progress data.ScanProgress) {
	// TODO: 更新进度显示
}

// RefreshData 刷新数据
func (s *ScanTab) RefreshData(hosts []data.HostInfo, groups []data.HostGroup) {
	s.hosts = hosts
	s.groups = groups
	
	// 更新主机选择器
	if s.hostSelector != nil {
		options := make([]string, len(hosts))
		for i, host := range hosts {
			options[i] = host.Name
		}
		s.hostSelector.Options = options
		s.hostSelector.Refresh()
	}
}
