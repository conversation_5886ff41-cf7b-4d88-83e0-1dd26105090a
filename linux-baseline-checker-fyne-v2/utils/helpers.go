package utils

import (
	"crypto/rand"
	"fmt"
	"strconv"
	"strings"
	"time"
	"unicode"

	"linux-baseline-checker/data"
)

// GenerateID 生成唯一ID
func GenerateID(prefix string) string {
	timestamp := time.Now().Unix()
	randomBytes := make([]byte, 4)
	rand.Read(randomBytes)

	return fmt.Sprintf("%s_%d_%x", prefix, timestamp, randomBytes)
}

// FormatDuration 格式化时间间隔
func FormatDuration(d time.Duration) string {
	if d < time.Second {
		return fmt.Sprintf("%dms", d.Milliseconds())
	} else if d < time.Minute {
		return fmt.Sprintf("%.1fs", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.1fm", d.Minutes())
	} else {
		return fmt.Sprintf("%.1fh", d.Hours())
	}
}

// FormatTime 格式化时间
func FormatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

// FormatTimeShort 格式化时间（短格式）
func FormatTimeShort(t time.Time) string {
	return t.Format("01-02 15:04")
}

// TruncateString 截断字符串
func TruncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}

	if maxLen <= 3 {
		return s[:maxLen]
	}

	return s[:maxLen-3] + "..."
}

// ValidateHostInfo 验证主机信息
func ValidateHostInfo(host data.HostInfo) []string {
	var errors []string

	if strings.TrimSpace(host.Name) == "" {
		errors = append(errors, "主机名称不能为空")
	}

	if strings.TrimSpace(host.Host) == "" {
		errors = append(errors, "主机地址不能为空")
	}

	if strings.TrimSpace(host.Port) == "" {
		errors = append(errors, "端口不能为空")
	} else {
		if port, err := strconv.Atoi(host.Port); err != nil || port < 1 || port > 65535 {
			errors = append(errors, "端口必须是1-65535之间的数字")
		}
	}

	if strings.TrimSpace(host.Username) == "" {
		errors = append(errors, "用户名不能为空")
	}

	if strings.TrimSpace(host.Password) == "" {
		errors = append(errors, "密码不能为空")
	}

	return errors
}

// ValidateHostGroup 验证主机组信息
func ValidateHostGroup(group data.HostGroup) []string {
	var errors []string

	if strings.TrimSpace(group.Name) == "" {
		errors = append(errors, "主机组名称不能为空")
	}

	return errors
}

// SanitizeString 清理字符串
func SanitizeString(s string) string {
	// 移除控制字符
	result := strings.Map(func(r rune) rune {
		if unicode.IsControl(r) && r != '\n' && r != '\r' && r != '\t' {
			return -1
		}
		return r
	}, s)

	return strings.TrimSpace(result)
}

// ParseKeyValue 解析键值对字符串
func ParseKeyValue(s string, separator string) map[string]string {
	result := make(map[string]string)

	lines := strings.Split(s, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, separator, 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			result[key] = value
		}
	}

	return result
}

// CalculateScore 计算总分
func CalculateScore(results []data.BaselineCheckResult) (int, int) {
	totalScore := 0
	maxScore := 0

	for _, result := range results {
		totalScore += result.Score
		maxScore += 100 // 假设每个检查项最高100分
	}

	return totalScore, maxScore
}

// CalculateScorePercentage 计算分数百分比
func CalculateScorePercentage(totalScore, maxScore int) float64 {
	if maxScore == 0 {
		return 0
	}
	return float64(totalScore) / float64(maxScore) * 100
}

// GetRiskLevel 根据分数获取风险等级
func GetRiskLevel(percentage float64) string {
	if percentage >= 90 {
		return "低"
	} else if percentage >= 70 {
		return "中等"
	} else if percentage >= 50 {
		return "高"
	} else {
		return "严重"
	}
}

// GetRiskColor 获取风险等级对应的颜色
func GetRiskColor(risk string) string {
	switch strings.ToLower(risk) {
	case "低":
		return "#28a745" // 绿色
	case "中等":
		return "#ffc107" // 黄色
	case "高":
		return "#fd7e14" // 橙色
	case "严重":
		return "#dc3545" // 红色
	default:
		return "#6c757d" // 灰色
	}
}

// GetStatusColor 获取状态对应的颜色
func GetStatusColor(status string) string {
	switch strings.ToLower(status) {
	case "通过", "成功", "完成":
		return "#28a745" // 绿色
	case "警告", "部分成功":
		return "#ffc107" // 黄色
	case "失败", "错误":
		return "#dc3545" // 红色
	case "运行中", "扫描中":
		return "#007bff" // 蓝色
	case "已取消":
		return "#6c757d" // 灰色
	default:
		return "#6c757d" // 灰色
	}
}

// FormatBytes 格式化字节数
func FormatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}

	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}

	units := []string{"KB", "MB", "GB", "TB", "PB"}
	return fmt.Sprintf("%.1f %s", float64(bytes)/float64(div), units[exp])
}

// IsValidIP 检查是否为有效IP地址
func IsValidIP(ip string) bool {
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}

	for _, part := range parts {
		if num, err := strconv.Atoi(part); err != nil || num < 0 || num > 255 {
			return false
		}
	}

	return true
}

// IsValidHostname 检查是否为有效主机名
func IsValidHostname(hostname string) bool {
	if len(hostname) == 0 || len(hostname) > 253 {
		return false
	}

	// 简单的主机名验证
	for _, char := range hostname {
		if !unicode.IsLetter(char) && !unicode.IsDigit(char) && char != '-' && char != '.' {
			return false
		}
	}

	return true
}

// FilterHosts 过滤主机列表
func FilterHosts(hosts []data.HostInfo, filter string) []data.HostInfo {
	if filter == "" {
		return hosts
	}

	filter = strings.ToLower(filter)
	var filtered []data.HostInfo

	for _, host := range hosts {
		if strings.Contains(strings.ToLower(host.Name), filter) ||
			strings.Contains(strings.ToLower(host.Host), filter) ||
			strings.Contains(strings.ToLower(host.Username), filter) {
			filtered = append(filtered, host)
		}
	}

	return filtered
}

// FilterGroups 过滤主机组列表
func FilterGroups(groups []data.HostGroup, filter string) []data.HostGroup {
	if filter == "" {
		return groups
	}

	filter = strings.ToLower(filter)
	var filtered []data.HostGroup

	for _, group := range groups {
		if strings.Contains(strings.ToLower(group.Name), filter) ||
			strings.Contains(strings.ToLower(group.Description), filter) {
			filtered = append(filtered, group)
		}
	}

	return filtered
}

// SortHostsByName 按名称排序主机
func SortHostsByName(hosts []data.HostInfo) []data.HostInfo {
	sorted := make([]data.HostInfo, len(hosts))
	copy(sorted, hosts)

	// 简单的冒泡排序
	for i := 0; i < len(sorted)-1; i++ {
		for j := 0; j < len(sorted)-i-1; j++ {
			if strings.ToLower(sorted[j].Name) > strings.ToLower(sorted[j+1].Name) {
				sorted[j], sorted[j+1] = sorted[j+1], sorted[j]
			}
		}
	}

	return sorted
}

// SortGroupsByName 按名称排序主机组
func SortGroupsByName(groups []data.HostGroup) []data.HostGroup {
	sorted := make([]data.HostGroup, len(groups))
	copy(sorted, groups)

	// 简单的冒泡排序
	for i := 0; i < len(sorted)-1; i++ {
		for j := 0; j < len(sorted)-i-1; j++ {
			if strings.ToLower(sorted[j].Name) > strings.ToLower(sorted[j+1].Name) {
				sorted[j], sorted[j+1] = sorted[j+1], sorted[j]
			}
		}
	}

	return sorted
}

// GetCheckCategorySummary 获取检查类别汇总
func GetCheckCategorySummary(results []data.BaselineCheckResult) map[string]map[string]int {
	summary := make(map[string]map[string]int)

	for _, result := range results {
		if summary[result.Category] == nil {
			summary[result.Category] = make(map[string]int)
		}
		summary[result.Category][result.Status]++
	}

	return summary
}

// GetRiskSummary 获取风险等级汇总
func GetRiskSummary(results []data.BaselineCheckResult) map[string]int {
	summary := make(map[string]int)

	for _, result := range results {
		summary[result.Risk]++
	}

	return summary
}

// ExportToCSV 导出为CSV格式
func ExportToCSV(results []data.ScanResult) string {
	var lines []string

	// CSV头部
	header := "主机名,主机地址,扫描时间,状态,总检查项,通过,失败,警告,跳过,总分,最高分,得分率"
	lines = append(lines, header)

	// 数据行
	for _, result := range results {
		scorePercentage := CalculateScorePercentage(result.TotalScore, result.MaxScore)
		line := fmt.Sprintf("%s,%s,%s,%s,%d,%d,%d,%d,%d,%d,%d,%.1f%%",
			result.HostName,
			result.Host,
			FormatTime(result.StartTime),
			result.Status,
			result.TotalChecks,
			result.PassedChecks,
			result.FailedChecks,
			result.WarningChecks,
			result.SkippedChecks,
			result.TotalScore,
			result.MaxScore,
			scorePercentage,
		)
		lines = append(lines, line)
	}

	return strings.Join(lines, "\n")
}
